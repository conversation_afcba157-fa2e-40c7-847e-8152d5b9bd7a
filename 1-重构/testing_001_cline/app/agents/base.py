from typing import Dict, Any, Generator
from abc import ABC, abstractmethod
from app.services.ai_service import ai_service

class Agent(ABC):
    def __init__(self, name: str, prompt_template: str):
        """
        Initialize base agent
        
        Args:
            name: Name of the agent
            prompt_template: Jinja2 template string for the agent's prompt
        """
        self.name = name
        self.prompt_template = prompt_template
        self.state: Dict[str, Any] = {}  # Store agent state between runs
        
    @abstractmethod
    async def run(self, **kwargs) -> Dict[str, Any]:
        """
        Run the agent's main logic
        
        Args:
            **kwargs: Parameters needed for the agent's operation
            
        Returns:
            Dict containing the results of the agent's operation
        """
        pass

    def _generate_code(self, **kwargs) -> Generator[Dict[str, Any], None, None]:
        """Generate code using the agent's prompt template"""
        return ai_service.generate_code_stream(
            self.prompt_template.format(**kwargs)
        )
        
    def reset_state(self):
        """Reset the agent's state"""
        self.state = {}