from typing import Dict, Any, Generator
from app.services.code_executor import CodeExecutor
from .base import Agent

class ChatAgent(Agent):
    def __init__(self):
        prompt_template = """
        Respond to the following user message in a helpful and natural way:
        
        User message: {message}
        """
        super().__init__("ChatAgent", prompt_template)
    
    async def run(self, message: str, **kwargs) -> Dict[str, Any]:
        content_stream = self._generate_code(message=message)
        response = ""
        
        for chunk in content_stream:
            if chunk.get("status") == "streaming":
                response += chunk.get("chunk", "")
            elif chunk.get("status") == "complete":
                # If we get a complete response, prefer that
                response = chunk.get("explanation", response)
                
        return {
            "type": "chat",
            "message": response,
            "success": True
        }

class CommandAgent(Agent):
    def __init__(self):
        prompt_template = """
        Generate and execute a command based on the following request:
        
        Request: {command_request}
        Additional context: {context}
        """
        super().__init__("CommandAgent", prompt_template)
    
    async def run(self, command_request: str, context: str = "", **kwargs) -> Dict[str, Any]:
        # For direct command execution, we don't need to generate code
        if command_request in ['pwd', 'ls', 'dir', 'whoami', 'date', 'echo']:
            result = CodeExecutor.execute_shell_command(command_request)
        else:
            # Generate the command
            content_stream = self._generate_code(
                command_request=command_request,
                context=context
            )
            
            command = ""
            for chunk in content_stream:
                if chunk.get("status") == "complete":
                    command = chunk.get("code", "")
                    break
            
            if not command:
                return {
                    "type": "command",
                    "success": False,
                    "error": "Failed to generate command"
                }
                
            # Execute the command
            result = CodeExecutor.execute_shell_command(command)
        
        return {
            "type": "command",
            "command": command_request,
            "output": result,
            "success": "Error:" not in result
        }