from flask import Flask
from app.core.agent_context import agent_context

def create_app():
    app = Flask(__name__)
    
    # Initialize agents after app creation but before registering blueprints
    from app.agents import create_agent
    agent_context.set_create_agent(create_agent)
    
    # Register blueprints
    from app.routes.main import main as main_blueprint
    app.register_blueprint(main_blueprint)
    
    return app