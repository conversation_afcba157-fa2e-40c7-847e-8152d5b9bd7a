import asyncio
import json
from flask import Blueprint, render_template, request, jsonify
from app.services.code_generator import code_generator
from app.services.code_executor import CodeExecutor
from app.agents import create_agent
from app.core.sse import SSE

main = Blueprint('main', __name__)

@main.route('/')
def home():
    return render_template('index.html')

@main.route('/ai/generate/stream', methods=['POST'])
def generate_code_stream():
    message = request.json.get('message', '')
    
    def generate():
        # First, generate the code that will use the agents
        generated_code = None
        code_type = None
        explanation = None
        json_content = ""
        in_json = False
        
        # Stream the code generation process
        for response in code_generator.generate_code(message):
            if response.get('status') == 'streaming':
                chunk = response.get('chunk', '')
                
                # Start collecting JSON when we see an opening brace
                if not in_json and '{' in chunk:
                    in_json = True
                    start_idx = chunk.find('{')
                    json_content = chunk[start_idx:]
                elif in_json:
                    json_content += chunk
                
                yield SSE.format_sse({
                    "type": "generation",
                    "chunk": chunk,
                    "status": "streaming"
                })
                
                # Try to parse complete JSON
                if in_json and json_content.count('{') == json_content.count('}'):
                    try:
                        parsed_json = json.loads(json_content)
                        generated_code = parsed_json.get('code')
                        code_type = parsed_json.get('type')
                        explanation = parsed_json.get('explanation')
                        
                        yield SSE.format_sse({
                            "status": "complete",
                            "type": code_type,
                            "code": generated_code,
                            "explanation": explanation
                        })
                        break
                    except json.JSONDecodeError:
                        continue
        
        if generated_code:
            try:
                # Execute the generated code
                result = CodeExecutor.execute_python_code(generated_code)
                
                yield SSE.format_sse({
                    "status": "execute",
                    "result": result
                })
                
            except Exception as e:
                yield SSE.format_sse({
                    "status": "error",
                    "error": str(e)
                })
                
    return SSE.stream(generate())

@main.route('/execute', methods=['POST'])
def execute_code():
    code = request.json.get('code', '')
    command_type = request.json.get('type', 'python')
    
    try:
        if command_type == 'python':
            result = CodeExecutor.execute_python_code(code)
        else:
            result = CodeExecutor.execute_shell_command(code)
        
        return jsonify({
            'success': True,
            'output': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'output': f"Error: {str(e)}"
        })