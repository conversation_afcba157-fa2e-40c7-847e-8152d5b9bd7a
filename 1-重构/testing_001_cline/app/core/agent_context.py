from typing import Dict, Any
import asyncio

class AgentContext:
    """Handles the execution context for agents"""
    
    def __init__(self):
        self._create_agent = None
    
    def set_create_agent(self, create_agent_func):
        """Set the create_agent function after initialization"""
        self._create_agent = create_agent_func
    
    async def chat(self, message: str) -> str:
        """Chat agent helper function"""
        if not self._create_agent:
            raise RuntimeError("Agent context not properly initialized")
            
        agent = self._create_agent("chat")
        result = await agent.run(message=message)
        return result.get('message', '')

    async def command(self, cmd: str, context: str = '') -> str:
        """Command agent helper function"""
        if not self._create_agent:
            raise RuntimeError("Agent context not properly initialized")
            
        agent = self._create_agent("command")
        result = await agent.run(command_request=cmd, context=context)
        return result.get('output', '')

    async def view_file(self, path: str) -> Dict[str, Any]:
        """View file agent helper function"""
        if not self._create_agent:
            raise RuntimeError("Agent context not properly initialized")
            
        agent = self._create_agent("view_file")
        return await agent.run(file_path=path)

    async def write_file(self, path: str, content_type: str, requirements: str = '') -> Dict[str, Any]:
        """Write file agent helper function"""
        if not self._create_agent:
            raise RuntimeError("Agent context not properly initialized")
            
        agent = self._create_agent("write_file")
        return await agent.run(
            file_path=path,
            content_type=content_type,
            requirements=requirements
        )

    def get_context(self) -> Dict[str, Any]:
        """Get the execution context with all agent functions"""
        return {
            'asyncio': asyncio,
            'chat': self.chat,
            'command': self.command,
            'view_file': self.view_file,
            'write_file': self.write_file,
        }

# Create a singleton instance
agent_context = AgentContext()