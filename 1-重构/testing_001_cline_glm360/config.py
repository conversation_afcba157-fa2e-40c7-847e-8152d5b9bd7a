import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")
    CLAUDE_MODEL = "claude-3-5-sonnet-20240620"
    ANTHROPIC_API_URL = "https://api.anthropic.com/v1/messages"
    MAX_EXECUTION_TIME = 10  # seconds
    
    # Existing functions that should be available
    EXISTING_FUNCTIONS = {
        "greet": """def greet(name):
    return f"Hello, {name}!"
"""
    }
