[2024-12-19 18:29:17,770] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-19 18:29:17,771] INFO _appmap.configuration: config: []
[2024-12-19 18:29:17,771] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/pip'})
[2024-12-19 18:29:17,783] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:29:27,754] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-19 18:29:27,755] INFO _appmap.configuration: config: []
[2024-12-19 18:29:27,755] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-19 18:29:27,759] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:29:27,962] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:35:00,408] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-19 18:35:00,409] INFO _appmap.configuration: config: []
[2024-12-19 18:35:00,409] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-19 18:35:00,413] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:35:00,627] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:35:36,152] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:36:06,072] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:36:31,395] INFO _appmap.env: appmap enabled: False
[2024-12-19 19:47:29,982] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-19 19:47:29,983] INFO _appmap.configuration: config: []
[2024-12-19 19:47:29,983] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-19 19:47:29,994] INFO _appmap.env: appmap enabled: False
[2024-12-19 19:47:30,208] INFO _appmap.env: appmap enabled: False
[2024-12-19 19:47:57,011] INFO _appmap.env: appmap enabled: False
[2024-12-19 19:48:28,106] INFO _appmap.env: appmap enabled: False
[2024-12-19 20:06:51,272] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-19 20:06:51,273] INFO _appmap.configuration: config: []
[2024-12-19 20:06:51,273] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-19 20:06:51,288] INFO _appmap.env: appmap enabled: False
[2024-12-19 20:06:51,509] INFO _appmap.env: appmap enabled: False
[2024-12-19 20:07:11,979] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:37:47,732] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 10:37:47,732] INFO _appmap.configuration: config: []
[2024-12-20 10:37:47,732] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 10:37:47,745] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:38:23,162] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 10:38:23,162] INFO _appmap.configuration: config: []
[2024-12-20 10:38:23,162] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 10:38:23,176] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:38:23,395] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:44:51,377] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:46:27,778] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 10:46:27,778] INFO _appmap.configuration: config: []
[2024-12-20 10:46:27,778] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 10:46:27,793] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:46:28,022] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:50:17,545] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 10:50:17,545] INFO _appmap.configuration: config: []
[2024-12-20 10:50:17,545] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 10:50:17,560] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:50:17,785] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:11:15,746] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 11:11:15,746] INFO _appmap.configuration: config: []
[2024-12-20 11:11:15,746] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 11:11:15,760] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:11:15,975] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:43:03,364] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 11:43:03,364] INFO _appmap.configuration: config: []
[2024-12-20 11:43:03,364] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 11:43:03,377] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:43:03,740] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:46:37,351] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 11:46:37,351] INFO _appmap.configuration: config: []
[2024-12-20 11:46:37,352] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 11:46:37,358] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:46:37,581] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:59:09,323] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 11:59:09,323] INFO _appmap.configuration: config: []
[2024-12-20 11:59:09,323] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 11:59:09,328] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:59:09,566] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:47:48,136] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 12:47:48,136] INFO _appmap.configuration: config: []
[2024-12-20 12:47:48,136] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 12:47:48,202] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:47:48,496] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:52:52,908] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 12:52:52,908] INFO _appmap.configuration: config: []
[2024-12-20 12:52:52,908] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 12:52:52,923] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:52:53,171] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:55:56,280] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:56:51,854] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 12:56:51,854] INFO _appmap.configuration: config: []
[2024-12-20 12:56:51,854] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 12:56:51,866] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:59:27,853] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 12:59:27,853] INFO _appmap.configuration: config: []
[2024-12-20 12:59:27,853] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 12:59:27,865] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:59:28,094] INFO _appmap.env: appmap enabled: False
[2024-12-20 13:01:01,432] INFO _appmap.env: appmap enabled: False
[2024-12-20 13:01:05,122] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 13:01:05,122] INFO _appmap.configuration: config: []
[2024-12-20 13:01:05,122] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 13:01:05,127] INFO _appmap.env: appmap enabled: False
[2024-12-20 13:01:05,304] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:10:41,704] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:14:49,062] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:14:49,062] INFO _appmap.configuration: config: []
[2024-12-20 14:14:49,062] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:14:49,076] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:14:49,321] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:17:03,853] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:17:03,854] INFO _appmap.configuration: config: []
[2024-12-20 14:17:03,854] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:17:03,868] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:17:04,094] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:22:37,471] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:22:37,471] INFO _appmap.configuration: config: []
[2024-12-20 14:22:37,471] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:22:37,483] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:22:37,701] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:24:45,751] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:25:10,166] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:25:10,166] INFO _appmap.configuration: config: []
[2024-12-20 14:25:10,166] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:25:10,172] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:25:10,378] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:27:27,593] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:27:34,420] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:27:34,420] INFO _appmap.configuration: config: []
[2024-12-20 14:27:34,420] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:27:34,426] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:27:34,598] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:31:29,248] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:31:29,248] INFO _appmap.configuration: config: []
[2024-12-20 14:31:29,248] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:31:29,265] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:31:29,523] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:33:31,293] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:33:31,293] INFO _appmap.configuration: config: []
[2024-12-20 14:33:31,293] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:33:31,308] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:33:31,546] INFO _appmap.env: appmap enabled: False
[2025-07-20 22:19:32,862] INFO _appmap.configuration: file: [no appmap.yml]
[2025-07-20 22:19:32,862] INFO _appmap.configuration: config: []
[2025-07-20 22:19:32,862] INFO _appmap.configuration: env: environ({'ELECTRON_RUN_AS_NODE': '1', 'COMMAND_MODE': 'unix2003', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '0', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'OLDPWD': '/', 'OLLAMA_ORIGINS': '*', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/usr/local/corplink/mdm/opt/corplink-mdm/bin:/usr/local/go/bin', 'PWD': '/', 'SHELL': '/bin/zsh', 'SHLVL': '0', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.IdfYYxdW9B/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/2901c5ac6db8a986a5666c3af51ff804d05af0d4', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'VSCODE_CWD': '/', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.10-main.sock', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/2901c5ac6db8a986a5666c3af51ff804d05af0d4/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"81b89199f95dc7f8a6123adce567118a.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/2901c5ac6db8a986a5666c3af51ff804d05af0d4","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/corrupted.info"}', 'VSCODE_PID': '24158', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.52206193.52206199', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', '__CFBundleIdentifier': 'com.microsoft.VSCode', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'POSTHOG_API_KEY': 'phc_jZKK9l8y7YXMN4J5tT0Idkmr9RMZrIUlfVSiaKyTesv', 'APPLICATION_INSIGHTS_NO_STATSBEAT': 'true', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.102.2025071609/translations/extensions/vscode.json-language-features.i18n.json', 'REQUESTS_CA_BUNDLE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'SSL_CERT_FILE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'CURL_CA_BUNDLE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'ELECTRON_NO_ASAR': '1', 'LC_CTYPE': 'UTF-8'})
[2025-07-20 22:19:32,873] INFO _appmap.env: appmap enabled: False
[2025-07-20 22:19:33,687] INFO _appmap.configuration: file: [no appmap.yml]
[2025-07-20 22:19:33,688] INFO _appmap.configuration: config: []
[2025-07-20 22:19:33,689] INFO _appmap.configuration: env: environ({'ELECTRON_RUN_AS_NODE': '1', 'COMMAND_MODE': 'unix2003', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '0', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'OLDPWD': '/', 'OLLAMA_ORIGINS': '*', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/usr/local/corplink/mdm/opt/corplink-mdm/bin:/usr/local/go/bin', 'PWD': '/', 'SHELL': '/bin/zsh', 'SHLVL': '0', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.IdfYYxdW9B/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/2901c5ac6db8a986a5666c3af51ff804d05af0d4', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'VSCODE_CWD': '/', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.10-main.sock', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/2901c5ac6db8a986a5666c3af51ff804d05af0d4/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"81b89199f95dc7f8a6123adce567118a.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/2901c5ac6db8a986a5666c3af51ff804d05af0d4","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/corrupted.info"}', 'VSCODE_PID': '24158', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.52206193.52206199', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', '__CFBundleIdentifier': 'com.microsoft.VSCode', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'POSTHOG_API_KEY': 'phc_jZKK9l8y7YXMN4J5tT0Idkmr9RMZrIUlfVSiaKyTesv', 'APPLICATION_INSIGHTS_NO_STATSBEAT': 'true', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.102.2025071609/translations/extensions/vscode.json-language-features.i18n.json', 'REQUESTS_CA_BUNDLE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'SSL_CERT_FILE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'CURL_CA_BUNDLE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'ELECTRON_NO_ASAR': '1', 'LC_CTYPE': 'UTF-8'})
[2025-07-20 22:19:33,719] INFO _appmap.env: appmap enabled: False
[2025-07-20 22:19:33,873] INFO _appmap.configuration: file: [no appmap.yml]
[2025-07-20 22:19:33,874] INFO _appmap.configuration: config: []
[2025-07-20 22:19:33,911] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'SSL_CERT_FILE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'REQUESTS_CA_BUNDLE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.102.2025071609/translations/extensions/vscode.json-language-features.i18n.json', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.52206193.52206199', 'POSTHOG_API_KEY': 'phc_jZKK9l8y7YXMN4J5tT0Idkmr9RMZrIUlfVSiaKyTesv', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/2901c5ac6db8a986a5666c3af51ff804d05af0d4/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"81b89199f95dc7f8a6123adce567118a.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/2901c5ac6db8a986a5666c3af51ff804d05af0d4","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.10-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '24158', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.IdfYYxdW9B/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/usr/local/corplink/mdm/opt/corplink-mdm/bin:/usr/local/go/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/2901c5ac6db8a986a5666c3af51ff804d05af0d4', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'CURL_CA_BUNDLE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'APPLICATION_INSIGHTS_NO_STATSBEAT': 'true', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2025-07-20 22:19:33,914] INFO _appmap.configuration: file: [no appmap.yml]
[2025-07-20 22:19:33,914] INFO _appmap.configuration: config: []
[2025-07-20 22:19:33,915] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'SSL_CERT_FILE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'REQUESTS_CA_BUNDLE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.102.2025071609/translations/extensions/vscode.json-language-features.i18n.json', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.52206193.52206199', 'POSTHOG_API_KEY': 'phc_jZKK9l8y7YXMN4J5tT0Idkmr9RMZrIUlfVSiaKyTesv', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/2901c5ac6db8a986a5666c3af51ff804d05af0d4/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"81b89199f95dc7f8a6123adce567118a.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/2901c5ac6db8a986a5666c3af51ff804d05af0d4","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.10-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '24158', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.IdfYYxdW9B/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/usr/local/corplink/mdm/opt/corplink-mdm/bin:/usr/local/go/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/2901c5ac6db8a986a5666c3af51ff804d05af0d4', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'CURL_CA_BUNDLE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'APPLICATION_INSIGHTS_NO_STATSBEAT': 'true', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2025-07-20 22:19:33,931] INFO _appmap.env: appmap enabled: False
[2025-07-20 22:19:33,934] INFO _appmap.env: appmap enabled: False
[2025-07-20 22:19:34,394] INFO _appmap.configuration: file: [no appmap.yml]
[2025-07-20 22:19:34,395] INFO _appmap.configuration: config: []
[2025-07-20 22:19:34,396] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'SSL_CERT_FILE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'REQUESTS_CA_BUNDLE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.102.2025071609/translations/extensions/vscode.json-language-features.i18n.json', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.52206193.52206199', 'POSTHOG_API_KEY': 'phc_jZKK9l8y7YXMN4J5tT0Idkmr9RMZrIUlfVSiaKyTesv', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/2901c5ac6db8a986a5666c3af51ff804d05af0d4/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"81b89199f95dc7f8a6123adce567118a.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/2901c5ac6db8a986a5666c3af51ff804d05af0d4","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.10-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '24158', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.IdfYYxdW9B/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/usr/local/corplink/mdm/opt/corplink-mdm/bin:/usr/local/go/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/2901c5ac6db8a986a5666c3af51ff804d05af0d4', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'CURL_CA_BUNDLE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'APPLICATION_INSIGHTS_NO_STATSBEAT': 'true', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2025-07-20 22:19:34,418] INFO _appmap.env: appmap enabled: False
[2025-07-20 22:24:55,840] INFO _appmap.env: appmap enabled: False
[2025-07-20 22:24:56,201] INFO _appmap.env: appmap enabled: False
[2025-07-20 22:26:39,279] INFO _appmap.configuration: file: [no appmap.yml]
[2025-07-20 22:26:39,279] INFO _appmap.configuration: config: []
[2025-07-20 22:26:39,278] INFO _appmap.configuration: file: [no appmap.yml]
[2025-07-20 22:26:39,279] INFO _appmap.configuration: config: []
[2025-07-20 22:26:39,279] INFO _appmap.configuration: env: environ({'ELECTRON_RUN_AS_NODE': '1', 'COMMAND_MODE': 'unix2003', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '0', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'OLDPWD': '/', 'OLLAMA_ORIGINS': '*', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/usr/local/corplink/mdm/opt/corplink-mdm/bin:/usr/local/go/bin', 'PWD': '/', 'SHELL': '/bin/zsh', 'SHLVL': '0', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.IdfYYxdW9B/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/2901c5ac6db8a986a5666c3af51ff804d05af0d4', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'VSCODE_CWD': '/', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.10-main.sock', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/2901c5ac6db8a986a5666c3af51ff804d05af0d4/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"81b89199f95dc7f8a6123adce567118a.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/2901c5ac6db8a986a5666c3af51ff804d05af0d4","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/corrupted.info"}', 'VSCODE_PID': '24158', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.52206193.52206199', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', '__CFBundleIdentifier': 'com.microsoft.VSCode', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'POSTHOG_API_KEY': 'phc_jZKK9l8y7YXMN4J5tT0Idkmr9RMZrIUlfVSiaKyTesv', 'APPLICATION_INSIGHTS_NO_STATSBEAT': 'true', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.102.2025071609/translations/extensions/vscode.json-language-features.i18n.json', 'REQUESTS_CA_BUNDLE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'SSL_CERT_FILE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'CURL_CA_BUNDLE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'ELECTRON_NO_ASAR': '1', 'LC_CTYPE': 'UTF-8'})
[2025-07-20 22:26:39,279] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'SSL_CERT_FILE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'REQUESTS_CA_BUNDLE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.102.2025071609/translations/extensions/vscode.json-language-features.i18n.json', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.52206193.52206199', 'POSTHOG_API_KEY': 'phc_jZKK9l8y7YXMN4J5tT0Idkmr9RMZrIUlfVSiaKyTesv', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/2901c5ac6db8a986a5666c3af51ff804d05af0d4/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"81b89199f95dc7f8a6123adce567118a.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/2901c5ac6db8a986a5666c3af51ff804d05af0d4","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.10-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '24158', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.IdfYYxdW9B/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/usr/local/corplink/mdm/opt/corplink-mdm/bin:/usr/local/go/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/2901c5ac6db8a986a5666c3af51ff804d05af0d4', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'CURL_CA_BUNDLE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'APPLICATION_INSIGHTS_NO_STATSBEAT': 'true', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2025-07-20 22:26:39,292] INFO _appmap.env: appmap enabled: False
[2025-07-20 22:26:39,293] INFO _appmap.env: appmap enabled: False
[2025-07-20 22:26:57,973] INFO _appmap.configuration: file: [no appmap.yml]
[2025-07-20 22:26:57,973] INFO _appmap.configuration: config: []
[2025-07-20 22:26:57,974] INFO _appmap.configuration: env: environ({'ELECTRON_RUN_AS_NODE': '1', 'COMMAND_MODE': 'unix2003', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '0', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'OLDPWD': '/', 'OLLAMA_ORIGINS': '*', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/usr/local/corplink/mdm/opt/corplink-mdm/bin:/usr/local/go/bin', 'PWD': '/', 'SHELL': '/bin/zsh', 'SHLVL': '0', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.IdfYYxdW9B/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/2901c5ac6db8a986a5666c3af51ff804d05af0d4', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'VSCODE_CWD': '/', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.10-main.sock', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/2901c5ac6db8a986a5666c3af51ff804d05af0d4/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"81b89199f95dc7f8a6123adce567118a.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/2901c5ac6db8a986a5666c3af51ff804d05af0d4","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/corrupted.info"}', 'VSCODE_PID': '24158', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.52206193.52206199', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', '__CFBundleIdentifier': 'com.microsoft.VSCode', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'POSTHOG_API_KEY': 'phc_jZKK9l8y7YXMN4J5tT0Idkmr9RMZrIUlfVSiaKyTesv', 'APPLICATION_INSIGHTS_NO_STATSBEAT': 'true', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.102.2025071609/translations/extensions/vscode.json-language-features.i18n.json', 'REQUESTS_CA_BUNDLE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'SSL_CERT_FILE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'CURL_CA_BUNDLE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'ELECTRON_NO_ASAR': '1', 'LC_CTYPE': 'UTF-8'})
[2025-07-20 22:26:57,986] INFO _appmap.env: appmap enabled: False
[2025-07-20 22:26:57,991] INFO _appmap.configuration: file: [no appmap.yml]
[2025-07-20 22:26:57,992] INFO _appmap.configuration: config: []
[2025-07-20 22:26:57,992] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'SSL_CERT_FILE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'REQUESTS_CA_BUNDLE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.102.2025071609/translations/extensions/vscode.json-language-features.i18n.json', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.52206193.52206199', 'POSTHOG_API_KEY': 'phc_jZKK9l8y7YXMN4J5tT0Idkmr9RMZrIUlfVSiaKyTesv', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/2901c5ac6db8a986a5666c3af51ff804d05af0d4/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"81b89199f95dc7f8a6123adce567118a.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/2901c5ac6db8a986a5666c3af51ff804d05af0d4","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/81b89199f95dc7f8a6123adce567118a.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.10-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '24158', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.IdfYYxdW9B/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/usr/local/corplink/mdm/opt/corplink-mdm/bin:/usr/local/go/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/2901c5ac6db8a986a5666c3af51ff804d05af0d4', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'CURL_CA_BUNDLE': '/Users/<USER>/.codegeex/mamba/envs/codegeex-agent/ssl/cacert.pem', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'APPLICATION_INSIGHTS_NO_STATSBEAT': 'true', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2025-07-20 22:26:58,005] INFO _appmap.env: appmap enabled: False
[2025-07-20 22:27:00,674] INFO _appmap.env: appmap enabled: False
[2025-07-20 22:27:00,908] INFO _appmap.env: appmap enabled: False
[2025-07-20 22:27:56,046] INFO _appmap.env: appmap enabled: False
[2025-07-20 22:27:56,318] INFO _appmap.env: appmap enabled: False
[2025-07-20 22:31:55,248] INFO _appmap.env: appmap enabled: False
[2025-07-20 22:31:55,491] INFO _appmap.env: appmap enabled: False
