from typing import Dict, Any, Generator, Optional
from abc import ABC, abstractmethod
from app.services.ai_service import ai_service
from app.services.prompt_manager import PromptManager

class Agent(ABC):
    def __init__(self, name: str, prompt_template: Optional[str] = None, prompt_name: Optional[str] = None):
        """
        Initialize base agent
        
        Args:
            name: Name of the agent
            prompt_template: Jinja2 template string for the agent's prompt (optional)
            prompt_name: Name of the prompt file to use from prompts directory (optional)
        """
        self.name = name
        self.prompt_template = prompt_template
        self.prompt_name = prompt_name
        self.prompt_manager = PromptManager() if prompt_name else None
        self.state: Dict[str, Any] = {}  # Store agent state between runs
        
    @abstractmethod
    async def run(self, **kwargs) -> Dict[str, Any]:
        """
        Run the agent's main logic
        
        Args:
            **kwargs: Parameters needed for the agent's operation
            
        Returns:
            Dict containing the results of the agent's operation
        """
        pass

    def _generate_code(self, **kwargs) -> Generator[Dict[str, Any], None, None]:
        """Generate code using the agent's prompt template or prompt file"""
        if self.prompt_name and self.prompt_manager:
            # Use PromptManager to render the prompt
            prompt = self.prompt_manager.render_prompt(self.prompt_name, **kwargs)
        else:
            # Use the traditional template string
            prompt = self.prompt_template.format(**kwargs)
        
        return ai_service.generate_code_stream(prompt)
        
    def reset_state(self):
        """Reset the agent's state"""
        self.state = {}
