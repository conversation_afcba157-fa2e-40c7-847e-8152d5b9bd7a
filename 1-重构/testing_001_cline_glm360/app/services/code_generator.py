import json
from typing import Dict, Any, Generator
from app.services.ai_service import ai_service
from app.agents import AGENT_REGISTRY

class CodeGenerator:
    def __init__(self):
        # Create the agent documentation for the prompt
        self.agent_docs = self._generate_agent_docs()
        
    def _generate_agent_docs(self) -> str:
        """Generate documentation of available agents for the prompt"""
        docs = []
        for agent_name, agent_class in AGENT_REGISTRY.items():
            params = []
            if agent_name == "chat":
                params.append("message: str - The message to respond to")
            elif agent_name == "command":
                params.append("command_request: str - The command to execute")
                params.append("context: str = '' - Additional context (optional)")
            elif agent_name == "view_file":
                params.append("file_path: str - Path to the file to read")
            elif agent_name == "write_file":
                params.append("file_path: str - Path where to create the file")
                params.append("content_type: str - Type of content to generate")
                params.append("requirements: str = '' - Additional requirements (optional)")
            
            docs.append(f"{agent_name}: {', '.join(params)}")
        
        return "\n".join(docs)
    
    def generate_code(self, user_input: str) -> Generator[Dict[str, Any], None, None]:
        """Generate code to handle the user's input using available agents"""
        prompt = f"""Based on the user's input, generate Python code that uses the available agents to handle the request.
        Available agents and their parameters:
        {self.agent_docs}
        
        Rules:
        1. Generate valid Python code that uses these agents
        2. For simple questions/greetings, use the chat agent
        3. For file operations, use view_file or write_file agents
        4. For system commands, use the command agent
        5. You can use multiple agents if needed
        6. Use proper Python syntax including async/await
        7. Always use correct indentation
        8. When using print statements, use f-strings for formatting
        9. For commands like pwd, ls, etc., use the command agent with exact command string
        10. Don't include outer async function definition - the executor will add it
        
        User input: {user_input}
        
        Respond in this exact JSON format:
        {{
            "type": "python",
            "code": "the Python code that uses the agents",
            "explanation": "explanation of what the code will do"
        }}
        """
        
        accumulated_response = ""
        streaming_started = False
        
        try:
            for chunk in ai_service.ask_claude_stream(prompt):
                if chunk:
                    accumulated_response += chunk
                    if streaming_started or chunk.strip():  # Skip initial empty chunks
                        streaming_started = True
                        yield {
                            'chunk': chunk,
                            'status': 'streaming'
                        }
                
                # Try to find and parse JSON in accumulated response
                if '{' in accumulated_response and '}' in accumulated_response:
                    start_idx = accumulated_response.find('{')
                    end_idx = accumulated_response.rfind('}') + 1
                    json_str = accumulated_response[start_idx:end_idx]
                    
                    try:
                        data = json.loads(json_str)
                        if all(key in data for key in ['type', 'code', 'explanation']):
                            yield {
                                'type': data['type'],
                                'code': data['code'].strip(),
                                'explanation': data['explanation'],
                                'status': 'complete'
                            }
                            return
                    except json.JSONDecodeError:
                        continue  # Keep collecting more chunks
                    
        except Exception as e:
            print(f"Error in generate_code: {str(e)}")
            yield {
                'status': 'error',
                'error': str(e)
            }
        
        accumulated_response = ""
        json_started = False
        
        for chunk in ai_service.ask_claude_stream(prompt):
            accumulated_response += chunk
            yield {
                'chunk': chunk,
                'status': 'streaming'
            }
            
            # Look for complete JSON in the response
            try:
                if '{' in accumulated_response and '}' in accumulated_response:
                    start_idx = accumulated_response.find('{')
                    end_idx = accumulated_response.rfind('}') + 1
                    possible_json = accumulated_response[start_idx:end_idx]
                    
                    data = json.loads(possible_json)
                    if all(key in data for key in ['type', 'code', 'explanation']):
                        yield {
                            'type': data['type'],
                            'code': data['code'],
                            'explanation': data['explanation'],
                            'status': 'complete'
                        }
                        break
            except json.JSONDecodeError:
                continue
            except Exception as e:
                print(f"Error in generate_code_stream: {str(e)}")
                yield {
                    'status': 'error',
                    'error': str(e)
                }
                break

# Create a singleton instance
code_generator = CodeGenerator()