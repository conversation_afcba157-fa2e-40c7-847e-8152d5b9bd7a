import os
from pathlib import Path
from jinja2 import Template, Environment, meta, TemplateError
from typing import Dict, Any, Optional, Set

class PromptManager:
    def __init__(self):
        self.prompts_dir = Path(__file__).parent.parent / 'prompts'
        self._cache: Dict[str, str] = {}
        
    def _load_prompt(self, prompt_name: str) -> str:
        """Load a prompt from file, using cache if available"""
        if prompt_name in self._cache:
            return self._cache[prompt_name]
            
        prompt_path = self.prompts_dir / f"{prompt_name}.txt"
        if not prompt_path.exists():
            raise FileNotFoundError(f"Prompt file not found: {prompt_path}")
            
        with open(prompt_path, 'r', encoding='utf-8') as f:
            prompt_template = f.read()
            self._cache[prompt_name] = prompt_template
            return prompt_template
    
    def render_prompt(self, prompt_name: str, **kwargs: Any) -> str:
        """
        Render a prompt template with given variables
        
        Args:
            prompt_name: Name of the prompt file (without .txt extension)
            **kwargs: Variables to be used in the template
            
        Returns:
            Rendered prompt string
        
        Raises:
            FileNotFoundError: If prompt file doesn't exist
            TemplateError: If there's an error in template syntax
            KeyError: If required variables are missing
        """
        try:
            # Load template string
            template_str = self._load_prompt(prompt_name)
            
            # Create Jinja2 Environment
            env = Environment()
            
            # Parse the template to get required variables
            ast = env.parse(template_str)
            required_vars: Set[str] = meta.find_undeclared_variables(ast)
            
            # Check if all required variables are provided
            missing_vars = required_vars - set(kwargs.keys())
            if missing_vars:
                raise KeyError(f"Missing required template variables: {', '.join(missing_vars)}")
            
            # Create and render the template
            template = Template(template_str)
            return template.render(**kwargs)
            
        except TemplateError as e:
            raise TemplateError(f"Template error in {prompt_name}: {str(e)}")
        except Exception as e:
            raise Exception(f"Error rendering prompt {prompt_name}: {str(e)}")