import os
from typing import Dict, Any, Optional, List
from .base import Agent
from app.services.prompt_manager import PromptManager

class ViewFileAgent(Agent):
    def __init__(self, chunk_size: int = 1000, max_rounds: int = 5):
        """
        Initialize ViewFileAgent
        
        Args:
            chunk_size: Number of tokens to read per round
            max_rounds: Maximum number of rounds to read
        """
        prompt_template = """
        Read and analyze the following content from the file.
        Return your analysis in a clear format.
        
        File content:
        {content}
        
        Current position: {current_position}
        Total rounds: {current_round}/{max_rounds}
        """
        super().__init__("ViewFileAgent", prompt_template)
        self.chunk_size = chunk_size
        self.max_rounds = max_rounds
        self.state = {
            "current_position": 0,
            "current_round": 0,
            "file_path": None
        }
    
    async def run(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """
        Read and analyze file content in chunks
        
        Args:
            file_path: Path to the file to read
            **kwargs: Additional parameters
            
        Returns:
            Dict containing analysis results and state
        """
        self.state["file_path"] = file_path
        
        if not os.path.exists(file_path):
            return {"error": f"File not found: {file_path}"}
            
        results = []
        
        while self.state["current_round"] < self.max_rounds:
            with open(file_path, 'r') as f:
                f.seek(self.state["current_position"])
                content = f.read(self.chunk_size)
                
                if not content:  # End of file
                    break
                    
                self.state["current_position"] = f.tell()
            
            # Generate analysis for this chunk
            analysis_code = self._generate_code(
                content=content,
                current_position=self.state["current_position"],
                current_round=self.state["current_round"] + 1,
                max_rounds=self.max_rounds
            )
            
            # Execute analysis
            result = self._execute_code(analysis_code)
            results.append(result)
            
            self.state["current_round"] += 1
        
        return {
            "results": results,
            "state": self.state,
            "complete": self.state["current_round"] >= self.max_rounds
        }

class WriteFileAgent(Agent):
    def __init__(self):
        """Initialize WriteFileAgent"""
        prompt_template = """
        Generate content for a file with the following specifications:
        
        File path: {file_path}
        Content type: {content_type}
        Additional requirements: {requirements}
        
        Generate the content in the specified format.
        """
        super().__init__("WriteFileAgent", prompt_template)
    
    async def run(self, 
                 file_path: str, 
                 content_type: str, 
                 requirements: str = "", 
                 **kwargs) -> Dict[str, Any]:
        """
        Generate and write content to a file
        
        Args:
            file_path: Path where to create the file
            content_type: Type of content to generate (e.g., "python", "text")
            requirements: Additional requirements for content generation
            **kwargs: Additional parameters
            
        Returns:
            Dict containing operation results
        """
        # Generate content
        content_stream = self._generate_code(
            file_path=file_path,
            content_type=content_type,
            requirements=requirements
        )
        
        generated_content = ""
        for chunk in content_stream:
            if chunk.get("status") == "streaming":
                generated_content += chunk.get("chunk", "")
            elif chunk.get("status") == "complete":
                generated_content = chunk.get("code", generated_content)
        
        # Create the directory if it doesn't exist
        os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
        
        # Write the content to file
        try:
            with open(file_path, 'w') as f:
                f.write(generated_content)
            
            return {
                "success": True,
                "file_path": file_path,
                "content_length": len(generated_content),
                "message": f"File created successfully at {file_path}"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "file_path": file_path
            }