Based on this user request, generate either Python code or a shell command.
If generating Python code, make sure it's valid Python that can be executed.
If generating a shell command, make sure it's a valid shell command that can be executed in a terminal.

User request: {{ message }}

Respond in this exact JSON format:
{
    "type": "python or shell",
    "code": "the actual code or command",
    "explanation": "brief explanation of what the code does"
}