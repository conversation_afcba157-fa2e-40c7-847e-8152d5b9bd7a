import subprocess
import tempfile
import os
import asyncio
from typing import Dict, Any
from config import Config
from app.core.agent_context import agent_context

class CodeExecutor:
    @staticmethod
    def clean_code(code: str) -> str:
        """Clean and normalize code"""
        # Split into lines and remove empty lines from start/end
        lines = [line for line in code.splitlines() if line.strip()]
        if not lines:
            return ""
            
        # Remove common leading whitespace from every line
        def _get_leading_spaces(line: str) -> int:
            return len(line) - len(line.lstrip())
            
        # Find minimum indentation
        min_indent = min(_get_leading_spaces(line) for line in lines)
        
        # Remove that amount of leading whitespace from every line
        cleaned_lines = [
            line[min_indent:] if _get_leading_spaces(line) >= min_indent else line.lstrip()
            for line in lines
        ]
        
        return "\n".join(cleaned_lines)

    @staticmethod
    def wrap_with_async_function(code: str) -> str:
        """Wrap code with async function while maintaining proper indentation"""
        # Clean the code first
        cleaned_code = CodeExecutor.clean_code(code)
        if not cleaned_code:
            return "async def handle_request():\n    return locals()"
            
        # Add the async function definition and indent the code
        wrapped_lines = ["async def handle_request():"]
        for line in cleaned_code.splitlines():
            wrapped_lines.append(f"    {line}")
            
        # Add return statement if not present
        if not any('return' in line for line in wrapped_lines):
            wrapped_lines.append("    return locals()")
            
        return "\n".join(wrapped_lines)

    @staticmethod
    async def execute_python_code_async(code: str) -> str:
        """Execute Python code with async support and agent context"""
        try:
            # Get execution context with agent functions
            context = agent_context.get_context()
            
            # Prepare the code
            final_code = (
                code if code.strip().startswith('async def')
                else CodeExecutor.wrap_with_async_function(code)
            )
            
            # Print for debugging
            print(f"Executing code:\n{final_code}")
            
            # Execute the code
            exec(final_code, context)
            result = await context['handle_request']()
            
            # Handle different result types
            if isinstance(result, (str, int, float, bool)):
                return str(result)
            elif isinstance(result, dict):
                if 'response' in result:
                    return str(result['response'])
                if 'result' in result:
                    return str(result['result'])
                # Return first non-callable, non-module value
                for value in result.values():
                    if not callable(value) and not isinstance(value, type(asyncio)):
                        return str(value)
            
            return str(result)

        except Exception as e:
            import traceback
            print(f"Code execution error: {str(e)}")
            print(f"Code that failed:\n{final_code}")
            print(traceback.format_exc())
            return f"Error: {str(e)}"

    @staticmethod
    def execute_python_code(code: str) -> str:
        """Execute Python code synchronously by running it in an event loop"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(CodeExecutor.execute_python_code_async(code))
            return result
        finally:
            loop.close()

    @staticmethod
    def execute_shell_command(command: str) -> str:
        """Execute shell command safely"""
        try:
            # Add basic safety checks
            if any(dangerous_cmd in command.lower() for dangerous_cmd in ['rm -rf', 'mkfs', 'dd']):
                return "Error: Command not allowed for safety reasons"
                
            result = subprocess.run(
                command, 
                shell=True, 
                capture_output=True, 
                text=True,
                timeout=Config.MAX_EXECUTION_TIME
            )
            output = result.stdout if result.returncode == 0 else f"Error: {result.stderr}"
        except subprocess.TimeoutExpired:
            output = f"Error: Command timed out after {Config.MAX_EXECUTION_TIME} seconds"
        except Exception as e:
            output = f"Error: {str(e)}"
        
        return output