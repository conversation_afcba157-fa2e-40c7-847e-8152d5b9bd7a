import json
import requests
from typing import Generator, Optional
from config import Config
from .prompt_manager import PromptManager

class AIService:
    def __init__(self):
        self.prompt_manager = PromptManager()

    def ask_claude(self, message: str) -> str:
        """Send a message to <PERSON> and get the response (non-streaming)"""
        headers = {
            "x-api-key": Config.ANTHROPIC_API_KEY,
            "anthropic-version": "2023-06-01",
            "content-type": "application/json"
        }
        data = {
            "model": Config.CLAUDE_MODEL,
            "max_tokens": 1024,
            "messages": [
                {"role": "user", "content": message}
            ]
        }

        response = requests.post(Config.ANTHROPIC_API_URL, headers=headers, json=data)
        try:
            reply = response.json()['content'][0]['text']
            return reply
        except Exception as e:
            print(f"Error in Claude response: {response.json()}")
            return None

    def ask_claude_stream(self, message: str) -> Generator[str, None, None]:
        """Send a message to <PERSON> and stream the response"""
        headers = {
            "x-api-key": Config.ANTHROPIC_API_KEY,
            "anthropic-version": "2023-06-01",
            "content-type": "application/json",
            "accept": "text/event-stream"
        }
        data = {
            "model": Config.CLAUDE_MODEL,
            "max_tokens": 1024,
            "messages": [
                {"role": "user", "content": message}
            ],
            "stream": True
        }

        try:
            response = requests.post(Config.ANTHROPIC_API_URL, headers=headers, json=data, stream=True)
            response.raise_for_status()
            
            for line in response.iter_lines():
                if not line or line.decode('utf-8').startswith(':'):  # Skip empty lines and comments
                    continue
                    
                line = line.decode('utf-8')
                if not line.startswith('data: '):
                    continue
                    
                data_str = line[6:]  # Remove 'data: ' prefix
                if data_str == '[DONE]':
                    break
                    
                try:
                    data = json.loads(data_str)
                    if data.get('type') == 'content_block_delta' and 'delta' in data:
                        text = data['delta'].get('text', '')
                        if text:
                            yield text
                except json.JSONDecodeError:
                    continue  # Skip invalid JSON
                    
        except requests.exceptions.RequestException as e:
            print(f"Request error: {e}")
            yield json.dumps({
                "error": str(e),
                "status": "error"
            })

    def detect_command_type(self, code: str) -> str:
        """Detect if the code is Python or shell command"""
        try:
            prompt = self.prompt_manager.render_prompt('command_type', code=code)
            response = self.ask_claude(prompt)
            response = response.lower().strip() if response else 'python'
            return 'python' if 'python' in response else 'shell'
        except Exception as e:
            print(f"Error detecting command type: {e}")
            return 'python'

    def generate_code_stream(self, message: str) -> Generator[dict, None, None]:
        """Generate code based on user message with streaming response"""
        try:
            prompt = self.prompt_manager.render_prompt('code_generation', message=message)
            accumulated_response = ""
            json_content = ""
            in_json = False
            
            for chunk in self.ask_claude_stream(prompt):
                accumulated_response += chunk
                yield {
                    'chunk': chunk,
                    'status': 'streaming'
                }
                
                # Look for complete JSON in the response
                if not in_json and '{' in chunk:
                    in_json = True
                    json_start = accumulated_response.find('{')
                    json_content = accumulated_response[json_start:]
                elif in_json:
                    json_content += chunk
                
                if in_json and json_content.count('{') == json_content.count('}') > 0:
                    try:
                        response_data = json.loads(json_content)
                        yield {
                            'type': response_data.get('type', 'python'),
                            'code': response_data.get('code', ''),
                            'explanation': response_data.get('explanation', ''),
                            'status': 'complete'
                        }
                        break
                    except json.JSONDecodeError:
                        continue
            
            # If we never found complete JSON, try one last time with the full response
            if not complete_json_found:
                print(f"Final accumulated response: {accumulated_response}")  # Debug log
                try:
                    # Try to extract JSON from the complete response
                    start_idx = accumulated_response.find('{')
                    end_idx = accumulated_response.rfind('}')
                    
                    if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                        json_str = accumulated_response[start_idx:end_idx + 1]
                        response_data = json.loads(json_str)
                        yield {
                            'type': response_data.get('type', 'python'),
                            'code': response_data.get('code', ''),
                            'explanation': response_data.get('explanation', ''),
                            'status': 'complete'
                        }
                except (json.JSONDecodeError, ValueError) as e:
                    print(f"Error parsing final response: {e}")  # Debug log
                    yield {
                        'type': 'python',
                        'code': "print('Error: Could not parse response')",
                        'explanation': "Failed to parse AI response",
                        'status': 'error'
                    }
                    
        except Exception as e:
            print(f"Error in generate_code_stream: {str(e)}")  # Debug log
            yield {
                'type': 'python',
                'code': "print('Error generating code')",
                'explanation': f"An error occurred: {str(e)}",
                'status': 'error'
            }

# Create a singleton instance
ai_service = AIService()