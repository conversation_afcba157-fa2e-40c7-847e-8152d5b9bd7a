from typing import Dict, Any, Generator
from app.services.code_executor import CodeExecutor
from app.services.prompt_manager import Prompt<PERSON>anager
from .base import Agent

class ChatAgent(Agent):
    def __init__(self):
        self.prompt_manager = PromptManager()
        super().__init__("ChatAgent", "")  # Empty template, will use PromptManager
    
    async def run(self, message: str, **kwargs) -> Dict[str, Any]:
        rendered_prompt = self.prompt_manager.render_prompt("chat", message=message)
        content_stream = self._generate_code_from_prompt(rendered_prompt)
        response = ""
        
        for chunk in content_stream:
            if chunk.get("status") == "streaming":
                response += chunk.get("chunk", "")
            elif chunk.get("status") == "complete":
                # If we get a complete response, prefer that
                response = chunk.get("explanation", response)
                
        return {
            "type": "chat",
            "message": response,
            "success": True
        }

class CommandAgent(Agent):
    def __init__(self):
        self.prompt_manager = PromptManager()
        super().__init__("CommandAgent", "")  # Empty template, will use PromptManager
    
    async def run(self, command_request: str, context: str = "", **kwargs) -> Dict[str, Any]:
        # For direct command execution, we don't need to generate code
        if command_request in ['pwd', 'ls', 'dir', 'whoami', 'date', 'echo']:
            result = CodeExecutor.execute_shell_command(command_request)
        else:
            # Generate the command
            rendered_prompt = self.prompt_manager.render_prompt(
                "command",
                command_request=command_request,
                context=context
            )
            content_stream = self._generate_code_from_prompt(rendered_prompt)
            
            command = ""
            for chunk in content_stream:
                if chunk.get("status") == "complete":
                    command = chunk.get("code", "")
                    break
            
            if not command:
                return {
                    "type": "command",
                    "success": False,
                    "error": "Failed to generate command"
                }
                
            # Execute the command
            result = CodeExecutor.execute_shell_command(command)
        
        return {
            "type": "command",
            "command": command_request,
            "output": result,
            "success": "Error:" not in result
        }