.message {
    padding: 12px;
    margin-bottom: 12px;
    border-radius: 8px;
    max-width: 85%;
}

.user-message {
    background-color: #e3f2fd;
    margin-left: auto;
    color: #1976d2;
}

.ai-message {
    background-color: #f5f5f5;
    margin-right: auto;
    color: #333;
}

.code-block {
    margin: 16px 0;
    border-radius: 8px;
    overflow: hidden;
    background-color: #1e1e1e;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.code-block pre {
    margin: 0;
    padding: 16px;
    font-family: 'Fira Code', monospace;
    font-size: 14px;
    line-height: 1.5;
    overflow-x: auto;
}

.code-block-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background-color: #2d2d2d;
    color: #fff;
}

.type-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    margin-left: 8px;
}

.type-python {
    background-color: #3572A5;
    color: white;
}

.type-shell {
    background-color: #4eaa25;
    color: white;
}

.execute-button {
    display: block;
    width: 100%;
    padding: 8px 16px;
    background-color: #2196f3;
    color: white;
    border: none;
    border-top: 1px solid #1e1e1e;
    cursor: pointer;
    transition: background-color 0.2s;
}

.execute-button:hover {
    background-color: #1976d2;
}

.execute-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.output {
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    font-family: monospace;
    white-space: pre-wrap;
    color: #333;
}

.error {
    color: #dc3545;
}

/* Scrollbar styling */
#chatMessages::-webkit-scrollbar {
    width: 8px;
}

#chatMessages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

#chatMessages::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

#chatMessages::-webkit-scrollbar-thumb:hover {
    background: #666;
}

/* Loading animation */
.loading {
    display: inline-block;
    margin-left: 8px;
}

.loading::after {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
