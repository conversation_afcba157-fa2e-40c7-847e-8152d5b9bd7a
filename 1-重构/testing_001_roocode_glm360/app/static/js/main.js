class CodeAssistant {
    constructor() {
        this.chatMessages = document.getElementById('chatMessages');
        this.userInput = document.getElementById('userInput');
        this.setupEventListeners();
    }

    setupEventListeners() {
        this.userInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        document.querySelector('button').addEventListener('click', () => this.sendMessage());
    }

    async sendMessage() {
        const message = this.userInput.value.trim();
        if (!message) return;

        this.userInput.value = '';
        this.appendMessage(message, 'user');

        try {
            // Create loading message
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'message ai-message loading';
            loadingDiv.textContent = 'Assistant: ';
            const streamingContent = document.createElement('span');
            loadingDiv.appendChild(streamingContent);
            this.chatMessages.appendChild(loadingDiv);
            this.scrollToBottom();

            // Setup SSE
            const response = await fetch('/ai/generate/stream', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message })
            });

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let accumulatedData = '';

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                // Decode the chunk and split by double newlines (SSE format)
                const chunk = decoder.decode(value);
                const events = chunk.split('\n\n');

                for (const event of events) {
                    if (!event.trim()) continue;

                    // Parse the SSE data
                    const dataMatch = event.match(/data: (.+)$/m);
                    if (!dataMatch) continue;

                    try {
                        const data = JSON.parse(dataMatch[1]);
                        
                        if (data.status === 'streaming') {
                            // Update the streaming content
                            streamingContent.textContent += data.chunk;
                            this.scrollToBottom();
                        } else if (data.status === 'complete' || data.status === 'execute') {
                            // Remove the loading message
                            loadingDiv.remove();
                            
                            if (data.status === 'complete') {
                                // Show the generated code
                                this.appendMessage(data.explanation, 'ai');
                                if (data.code) {
                                    this.appendCodeBlock(data.code, data.type);
                                }
                            } else if (data.status === 'execute') {
                                // Show execution results
                                if (data.result) {
                                    const result = typeof data.result === 'string' 
                                        ? data.result 
                                        : JSON.stringify(data.result, null, 2);
                                    this.appendMessage(`Output: ${result}`, 'output');
                                }
                            }
                        } else if (data.status === 'error') {
                            loadingDiv.remove();
                            this.appendMessage(`Error: ${data.explanation}`, 'error');
                        }
                    } catch (error) {
                        console.error('Error parsing SSE data:', error);
                    }
                }
            }
        } catch (error) {
            this.appendMessage(`Error: ${error.message}`, 'error');
        }
    }

    appendMessage(message, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;
        
        if (type === 'user') {
            messageDiv.textContent = `You: ${message}`;
        } else if (type === 'ai') {
            messageDiv.textContent = `Assistant: ${message}`;
        } else {
            messageDiv.textContent = message;
        }
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    appendCodeBlock(code, type) {
        const codeBlock = document.createElement('div');
        codeBlock.className = 'code-block';

        // Create header
        const header = document.createElement('div');
        header.className = 'code-block-header';
        header.innerHTML = `
            <span>Code ${type === 'python' ? 'Python' : 'Shell'}
                <span class="type-badge type-${type}">${type}</span>
            </span>
        `;
        codeBlock.appendChild(header);

        // Create code content
        const pre = document.createElement('pre');
        const codeElement = document.createElement('code');
        codeElement.className = `language-${type === 'python' ? 'python' : 'bash'}`;
        codeElement.textContent = code;
        pre.appendChild(codeElement);
        codeBlock.appendChild(pre);

        // Create execute button
        const executeButton = document.createElement('button');
        executeButton.className = 'execute-button';
        executeButton.textContent = `Execute ${type} code`;
        executeButton.onclick = () => this.executeCode(code, type, executeButton);
        codeBlock.appendChild(executeButton);

        this.chatMessages.appendChild(codeBlock);
        this.scrollToBottom();

        // Highlight code
        Prism.highlightElement(codeElement);
    }

    async executeCode(code, type, button) {
        button.disabled = true;
        button.innerHTML = 'Executing... <span class="loading"></span>';

        try {
            const response = await fetch('/execute', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ code, type })
            });

            const result = await response.json();
            
            // Create output display
            const outputDiv = document.createElement('div');
            outputDiv.className = `output${result.output.includes('Error:') ? ' error' : ''}`;
            outputDiv.textContent = result.output;
            
            // Remove any existing output
            const existingOutput = button.parentNode.querySelector('.output');
            if (existingOutput) {
                existingOutput.remove();
            }
            
            button.parentNode.appendChild(outputDiv);
        } catch (error) {
            this.appendMessage(`Error: ${error.message}`, 'error');
        } finally {
            button.disabled = false;
            button.textContent = `Execute ${type} code`;
        }
    }

    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }
}

// Initialize the app
document.addEventListener('DOMContentLoaded', () => {
    window.codeAssistant = new CodeAssistant();
});