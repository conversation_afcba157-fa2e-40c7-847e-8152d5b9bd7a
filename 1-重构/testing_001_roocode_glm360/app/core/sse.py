from flask import Response
from typing import Generator, Any
import json

class SSE:
    @staticmethod
    def format_sse(data: Any, event: str = None) -> str:
        """Format data according to SSE spec"""
        if isinstance(data, (dict, list)):
            msg = f"data: {json.dumps(data)}\n"
        else:
            msg = f"data: {data}\n"
            
        if event is not None:
            msg = f"event: {event}\n{msg}"
        return f"{msg}\n"

    @staticmethod
    def stream(generator_function: Generator) -> Response:
        """Create a response streaming the data from the generator"""
        return Response(
            generator_function,
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'X-Accel-Buffering': 'no',  # Disable buffering in Nginx
                'Content-Type': 'text/event-stream',
                'Access-Control-Allow-Origin': '*'  # Enable CORS
            }
        )