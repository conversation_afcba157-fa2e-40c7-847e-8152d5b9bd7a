import asyncio
import json
import time
from quart import Blueprint, render_template, request, jsonify, Response
from app.services.code_generator import code_generator
from app.services.code_executor import CodeExecutor
from app.agents import create_agent
from app.core.sse import SSE

main = Blueprint('main', __name__)

@main.route('/')
async def home():
    timestamp = int(time.time())
    return await render_template('index.html', timestamp=timestamp)

@main.route('/ai/generate/stream', methods=['POST'])
async def generate_code_stream():
    data = await request.get_json()
    message = data.get('message', '')
    
    async def generate():
        # Stream the code generation process
        for response in code_generator.generate_code(message):
            task_status = response.get('task_status', {'status': 'Incomplete'})
            
            # 流式输出过程中
            if response.get('status') == 'streaming':
                yield await SSE.format_sse({
                    "type": "generation",
                    "chunk": response.get('chunk', ''),
                    "status": "streaming",
                    "task_status": task_status
                })
            # 生成完毕
            elif response.get('status') == 'complete':
                yield await SSE.format_sse({
                    "status": "complete",
                    "type": response.get('type'),
                    "code": response.get('code', None),  
                    "analysis": response.get('analysis'),
                    "chat": response.get('chat'),
                    "task_status": task_status
                })
                
            # 生成失败
            elif response.get('status') == 'error':
                yield await SSE.format_sse({
                    "status": "error",
                    "error": response.get('error'),
                    "task_status": task_status
                })
    
    return Response(generate(), mimetype='text/event-stream')

@main.route('/ai/generate/execute', methods=['POST'])
async def execute_code_agent():
    request_data = await request.get_json()
    if not request_data or 'code' not in request_data:
        return jsonify({'status': 'error', 'error': 'No code provided'}), 400

    task_status = {'status': 'Complete'}
    
    async def generate():
        try:
            result = await CodeExecutor.execute_python_code(request_data['code'])
            # Parse the result to extract agent type if it's in the expected format
            agent_type = None
            if isinstance(result, str):
                if result.startswith('Output: command:'):
                    agent_type = 'command'
                elif '📄 File Content:' in result:
                    agent_type = 'viewfile'
                elif '✍️ File Operation:' in result:
                    agent_type = 'writefile'
            
            data = {
                "status": "execute",
                "result": result,
                "agent_type": agent_type,
                "task_status": task_status
            }
            yield await SSE.format_sse(data)
        except Exception as e:
            task_status['status'] = 'Incomplete'
            data = {
                "status": "error",
                "error": str(e),
                "task_status": task_status
            }
            yield await SSE.format_sse(data)

    return Response(
        generate(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'
        }
    )

@main.route('/execute', methods=['POST'])
async def execute_code():
    data = await request.get_json()
    code = data.get('code', '')
    command_type = data.get('type', 'python')
    
    try:
        if command_type == 'python':
            result = await CodeExecutor.execute_python_code(code)
        else:
            result = await CodeExecutor.execute_shell_command(code)
        
        return jsonify({
            'success': True,
            'output': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'output': f"Error: {str(e)}"
        })