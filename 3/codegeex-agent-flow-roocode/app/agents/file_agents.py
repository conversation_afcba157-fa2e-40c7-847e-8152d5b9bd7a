import os
from typing import Dict, Any, Optional, List
from .base import Agent
from app.services.prompt_manager import Prompt<PERSON>anager
from app.services.ai_service import ai_service
from app.core.sse import SSE

class ViewFileAgent(Agent):
    def __init__(self, chunk_size: int = 1000, max_rounds: int = 5):
        """
        Initialize ViewFileAgent
        
        Args:
            chunk_size: Number of tokens to read per round
            max_rounds: Maximum number of rounds to read
        """
        super().__init__("ViewFileAgent")
        self.prompt_manager = PromptManager()
        self.chunk_size = chunk_size
        self.max_rounds = max_rounds
        self.state = {
            "current_position": 0,
            "current_round": 0,
            "file_path": None
        }

    async def run(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """
        Read and analyze a file
        
        Args:
            file_path: Path to the file to read
        """
        try:
            if not os.path.exists(file_path):
                return {
                    "type": "viewfile",
                    "output": f"File not found: {file_path}",
                    "execution_output": None,
                    "success": False
                }

            with open(file_path, 'r') as f:
                content = f.read()

            return {
                "type": "viewfile",
                "output": content,  # The file content
                "execution_output": None,  # No execution for viewing
                "success": True
            }
            
        except Exception as e:
            return {
                "type": "viewfile",
                "output": str(e),
                "execution_output": None,
                "success": False
            }

class CreateFileAgent(Agent):
    def __init__(self):
        """
        Initialize CreateFileAgent
        
        This agent creates empty files. It handles directory creation
        and path validation.
        """
        super().__init__("CreateFileAgent")
        self.prompt_manager = PromptManager()

    async def run(self, 
                 file_path: str,
                 **kwargs) -> Dict[str, Any]:
        """
        Generate a file path and create an empty file based on user request
        
        Args:
            file_path: Path to the file to create
            **kwargs: Additional parameters
        """
        try:
            
            if not file_path:
                return {
                    "type": "createfile",
                    "output": "Failed to generate file path",
                    "execution_output": None,
                    "success": False
                }
            
            # Create directory if it doesn't exist
            directory = os.path.dirname(os.path.abspath(file_path))
            os.makedirs(directory, exist_ok=True)
            
            # Create empty file
            with open(file_path, 'w') as f:
                pass
                
            return {
                "type": "createfile",
                "output": f"Created empty file at: {file_path}",
                "execution_output": file_path,
                "success": True
            }
            
        except Exception as e:
            return {
                "type": "createfile",
                "output": str(e),
                "execution_output": None,
                "success": False
            }

class WriteFileAgent(Agent):
    def __init__(self):
        """
        Initialize WriteFileAgent
        
        This agent generates and writes code content to existing files.
        It analyzes the request and generates well-structured code content
        using a template-based approach.
        """
        super().__init__("WriteFileAgent")
        self.prompt_manager = PromptManager()

    async def run(self, 
                 user_request: str,
                 filepath: str,
                 existing_code: str = "",
                 **kwargs) -> Dict[str, Any]:
        """
        Generate and write content to an existing file
        
        Args:
            user_request: Description of what code to generate
            filepath: Path to the file to write to
            existing_code: Optional existing code for context
            **kwargs: Additional parameters
        """
        try:
            if not os.path.exists(filepath):
                return {
                    "type": "writefile",
                    "output": f"File does not exist: {filepath}",
                    "execution_output": None,
                    "success": False
                }

            # Get prompt from prompt manager
            prompt = self.prompt_manager.render_prompt(
                self.name,
                user_request=user_request,
                filepath=filepath,
                existing_code=existing_code
            )
            
            # Get response from Claude
            response = ai_service.ask_claude(prompt)
            
            # Extract content from response
            tags = self.prompt_manager.parse_response_tags(response)
            code_content = tags.get('code_content', '').strip()
            
            if not code_content:
                return {
                    "type": "writefile",
                    "output": "Failed to generate content",
                    "execution_output": None,
                    "success": False
                }
            
            # Write the content to file
            with open(filepath, 'w') as f:
                f.write(code_content)
                
            return {
                "type": "writefile",
                "output": code_content,
                "execution_output": f"Wrote content to: {filepath}",
                "success": True
            }
            
        except Exception as e:
            return {
                "type": "writefile",
                "output": str(e),
                "execution_output": None,
                "success": False
            }