import os
import importlib
from pathlib import Path
from typing import Dict, Type
from .base import Agent
from .utils import create_agent, run_agent_pipeline, validate_agent_config

# Automatically discover and import agent modules
agents_dir = Path(__file__).parent
AGENT_REGISTRY: Dict[str, Type[Agent]] = {}

def _initialize_agents():
    """Automatically discover and register all agent classes"""
    # Import all python files in the agents directory
    for file in agents_dir.glob("*.py"):
        if file.stem in ["__init__", "base", "utils"]:
            continue
            
        # Import the module
        module_name = f"app.agents.{file.stem}"
        module = importlib.import_module(module_name)
        
        # Find all Agent subclasses in the module
        for attr_name in dir(module):
            attr = getattr(module, attr_name)
            if (isinstance(attr, type) and 
                issubclass(attr, Agent) and 
                attr != Agent):
                # Register the agent using its name without 'Agent' suffix
                agent_name = attr_name.replace('Agent', '').lower()
                AGENT_REGISTRY[agent_name] = attr

# Initialize agents on import
_initialize_agents()

# Export all discovered agents and utilities
__all__ = (
    ['Agent', 'create_agent', 'run_agent_pipeline', 'validate_agent_config', 'AGENT_REGISTRY'] +
    [agent.__name__ for agent in AGENT_REGISTRY.values()]
)