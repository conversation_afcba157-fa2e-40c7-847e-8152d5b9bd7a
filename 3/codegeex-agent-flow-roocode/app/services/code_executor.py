import subprocess
import os
import asyncio
import json
from typing import Dict, Any
from app.core.agent_context import agent_context
from config import Config

# Default timeout for command execution (in seconds)
DEFAULT_COMMAND_TIMEOUT = Config.COMMAND_TIMEOUT
WORK_DIR = Config.WORK_DIR

class CodeExecutor:
    @staticmethod
    def clean_code(code: str) -> str:
        """Clean and normalize code"""
        # Split into lines and remove empty lines from start/end
        lines = [line for line in code.splitlines() if line.strip()]
        if not lines:
            return ""
            
        # Remove common leading whitespace from every line
        def _get_leading_spaces(line: str) -> int:
            return len(line) - len(line.lstrip())
            
        # Find minimum indentation
        min_indent = min(_get_leading_spaces(line) for line in lines)
        
        # Remove that amount of leading whitespace from every line
        cleaned_lines = [
            line[min_indent:] if _get_leading_spaces(line) >= min_indent else line.lstrip()
            for line in lines
        ]
        
        return "\n".join(cleaned_lines)

    @staticmethod
    def _get_leading_spaces(line: str) -> int:
        """Get number of leading spaces in a line"""
        return len(line) - len(line.lstrip())

    @staticmethod
    def wrap_with_async_function(code: str) -> str:
        """Wrap code with async function while maintaining proper indentation"""
        # Clean the code first
        cleaned_code = CodeExecutor.clean_code(code)
        
        # Add the async function definition and indent the code
        wrapped_lines = ["async def handle_request():"]
        for line in cleaned_code.splitlines():
            wrapped_lines.append(f"    {line}")
            
        # Add return statement if not present
        if not any('return' in line for line in wrapped_lines):
            wrapped_lines.append("    return locals()")
            
        return "\n".join(wrapped_lines)

    @staticmethod
    def format_output(result: Dict[str, Any]) -> str:
        """Format the output based on agent type"""
        if not isinstance(result, dict):
            return str(result)
            
        agent_type = result.get('type', '')
        output = result.get('output', '')
        execution_output = result.get('execution_output', '')
        success = result.get('success', True)
            
        # Uniform JSON format for all other agent types
        return json.dumps({
            "type": agent_type,
            "output": output,
            "execution_output": execution_output if execution_output else "",
            "success": success
        })

    @staticmethod
    async def execute_python_code(code: str) -> str:
        """Execute Python code with async support and agent context"""
        try:
            # Get execution context with agent functions
            context = agent_context.get_context()
            
            # Prepare the code
            final_code = (
                code if code.strip().startswith('async def')
                else CodeExecutor.wrap_with_async_function(code)
            )
            
            # Print for debugging
            print(f"Executing code:\n{final_code}")
            
            # Execute the code
            exec(final_code, context)
            result = await context['handle_request']()
            
            # Format the result
            return CodeExecutor.format_output(result)

        except Exception as e:
            import traceback
            print(f"Code execution error: {str(e)}")
            print(f"Code that failed:\n{final_code}")
            print(traceback.format_exc())
            return f"Error: {str(e)}"


    @staticmethod
    async def execute_shell_command(command: str) -> str:
        """Execute shell command safely"""
        try:
            # Run the command with timeout
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=Config.WORK_DIR
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=DEFAULT_COMMAND_TIMEOUT
                )
                
                if process.returncode != 0:
                    return f"Error: {stderr.decode()}"
                
                return stdout.decode()
                
            except asyncio.TimeoutError:
                try:
                    process.kill()
                except ProcessLookupError:
                    pass
                return "Error: Command execution timed out"
                
        except Exception as e:
            return f"Error: {str(e)}"