// src/messageManager.js
import { langMap } from './constants.js';

export class MessageManager {
    constructor(chatMessages, taskStatus, codeAssistant) {
        this.chatMessages = chatMessages;
        this.taskStatus = taskStatus;
        this.codeAssistant = codeAssistant;
    }

    // Process the incoming message and manage the loading state
    async processMessage(message) {
        try {
            // Create loading message
            const loadingDiv = this.createLoadingMessage();
            
            // Fetch and process the response
            const response = await this.fetchAIResponse(message);
            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            // Process the stream
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const events = chunk.split('\n\n');
                
                for (const event of events) {
                    await this.processSSEEvent(event, loadingDiv);
                }
            }
        } catch (error) {
            console.error('Error processing message:', error);
            throw error;
        }
    }

    // Create a loading message element
    createLoadingMessage() {
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'message ai-message loading';
        loadingDiv.textContent = 'Assistant: ';
        const streamingContent = document.createElement('span');
        loadingDiv.appendChild(streamingContent);
        this.chatMessages.appendChild(loadingDiv);
        this.scrollToBottom();
        return loadingDiv;
    }

    // Fetch AI response from the server
    async fetchAIResponse(message) {
        return await fetch('/ai/generate/stream', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ message })
        });
    }

    // Process individual SSE events
    async processSSEEvent(event, loadingDiv) {
        if (!event.trim()) return;

        const dataMatch = event.match(/data: (.+)$/m);
        if (!dataMatch) return;

        try {
            const data = JSON.parse(dataMatch[1]);
            
            if (data.status === 'streaming') {
                this.updateStreamingContent(loadingDiv, data.chunk);
            } else {
                loadingDiv.remove();
                await this.handleSSEData(data);
            }
        } catch (error) {
            console.error('Failed to process SSE event:', error);
            throw error;
        }
    }

    // Update streaming content in the loading message
    updateStreamingContent(loadingDiv, chunk) {
        const streamingContent = loadingDiv.querySelector('span');
        streamingContent.textContent += chunk;
        this.scrollToBottom();
    }

    // Handle the SSE data based on its status
    async handleSSEData(data) {
        // Update task status if present
        if (data.task_status) {
            this.updateTaskStatus(data.task_status);
        }

        if (data.status === 'complete') {
            this.handleCompleteData(data);
        }
    }

    // Handle complete data by adding analysis, chat messages, and code
    handleCompleteData(data) {
        if (data.analysis) {
            this.appendContentToChat('ai', data.analysis);
        }
        
        if (data.chat) {
            this.appendContentToChat('ai', `Codii: ${data.chat}`);
        }

        // Handle Python code if present
        if (data.code) {
            const codeBlock = this.createCodeBlock(data.code, data.type);
            this.chatMessages.appendChild(codeBlock);
            this.scrollToBottom();
            // Execute the generated code
            this.codeAssistant.executeGeneratedCode(data.code);
        }
    }

    // Create code block for Python code
    createCodeBlock(code, type) {
        const codeBlock = document.createElement('div');
        codeBlock.className = 'code-block';

        // Create header
        const header = document.createElement('div');
        header.className = 'code-block-header';
        header.innerHTML = `
            <span>Code ${langMap[type].display}
                <span class="type-badge type-${type}">${type}</span>
            </span>
        `;
        codeBlock.appendChild(header);

        // Create code content
        const pre = document.createElement('pre');
        pre.className = 'line-numbers';
        
        const codeElement = document.createElement('code');
        codeElement.className = `language-${langMap[type].language}`;
        codeElement.textContent = code;
        
        pre.appendChild(codeElement);
        codeBlock.appendChild(pre);

        // Create execute button
        const executeButton = document.createElement('button');
        executeButton.className = 'execute-button';
        executeButton.textContent = `Execute ${type} code`;
        executeButton.onclick = () => this.codeAssistant.executeCode(code, type, executeButton);
        codeBlock.appendChild(executeButton);

        // Trigger Prism to highlight the code
        if (typeof Prism !== 'undefined') {
            Prism.highlightElement(codeElement);
        }

        return codeBlock;
    }

    // Update the task status display
    updateTaskStatus(status) {
        this.taskStatus.textContent = status;
        this.taskStatus.className = 'task-status ' + status.toLowerCase();
    }

    // Append content to chat with proper formatting
    appendContentToChat(type, content) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;
        this.appendSimpleContent(messageDiv, content, this.chatMessages);
    }

    // Append simple content to a target panel
    appendSimpleContent(messageDiv, content, targetPanel) {
        messageDiv.textContent = typeof content === 'string' ? content : JSON.stringify(content, null, 2);
        targetPanel.appendChild(messageDiv);
        this.scrollToBottom();
    }

    // Utility to scroll chat to bottom
    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }
}