import { langMap, fileExtensionToLanguageMap, AGENT_TYPES } from './constants.js';
import { MessageManager } from './messageManager.js';

class CodeAssistant {
    constructor() {
        // Initialize DOM elements
        this.chatMessages = document.getElementById('chatMessages');
        this.outputMessages = document.getElementById('outputMessages');
        this.userInput = document.getElementById('userInput');
        this.sendButton = document.getElementById('sendButton');
        this.panelDivider = document.getElementById('panelDivider');
        this.taskStatus = document.getElementById('taskStatus');
        
        // Initialize MessageManager with reference to this instance
        this.messageManager = new MessageManager(this.chatMessages, this.taskStatus, this);
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        this.sendButton.addEventListener('click', () => this.sendMessage());
        this.userInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
    }

    // Send message handling
    async sendMessage() {
        const message = this.userInput.value.trim();
        if (!message) return;

        // Clear input
        this.userInput.value = '';
        
        // Append user message using MessageManager
        this.messageManager.appendContentToChat('user', message);
        
        // Reset task status
        this.taskStatus.textContent = 'Incomplete';
        this.taskStatus.className = 'task-status incomplete';

        try {
            await this.messageManager.processMessage(message);
        } catch (error) {
            this.messageManager.appendContentToChat('error', `Error: ${error.message}`);
        }
    }

    // Keep the existing agent-related methods for now
    async executeGeneratedCode(code) {
        try {
            const response = await fetch('/ai/generate/execute', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    code: code,
                    type: 'python'
                })
            });

            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.trim() === '' || !line.startsWith('data: ')) continue;

                    const eventData = JSON.parse(line.slice(6));
                    if (eventData.status === 'execute' && eventData.result) {
                        const resultObj = typeof eventData.result === 'string'
                            ? JSON.parse(eventData.result)
                            : eventData.result;
                        this.displayExecutionResults(resultObj, eventData);
                    } else if (eventData.status === 'error') {
                        console.error('Execution error:', eventData.error);
                        this.appendMessage(`Error executing code: ${eventData.error}`, 'error');
                    }
                }
            }
        } catch (error) {
            console.error('Failed to execute code:', error);
            this.appendMessage(`Error executing code: ${error.message}`, 'error');
        }
    }

    displayExecutionResults(resultObj, data) {
        const outputDiv = document.createElement('div');
        outputDiv.className = 'message output-message';
        
        const agentType = data?.agent_type || resultObj.type;
        const agentConfig = Object.values(AGENT_TYPES).find(config => config.type === agentType);
        
        if (agentConfig) {
            outputDiv.classList.add(agentConfig.type);
            
            const badge = document.createElement('span');
            badge.className = `agent-badge ${agentConfig.type}`;
            badge.textContent = agentConfig.displayName;
            outputDiv.appendChild(badge);
        }
        
        if (agentConfig?.hasCodeOutput) {
            this.appendCodeOutput(outputDiv, resultObj);
            
            if (agentConfig.type === AGENT_TYPES.COMMAND.type) {
                const executionContainer = document.createElement('div');
                executionContainer.className = 'execution-container';
                outputDiv.appendChild(executionContainer);
    
                this.createConfirmationButtons(outputDiv, resultObj, (accepted, result) => {
                    if (accepted) {
                        this.executeCommand(result.output, executionContainer);
                    }
                });
            }
        } else {
            const outputContent = document.createElement('span');
            outputContent.textContent = resultObj.output;
            outputDiv.appendChild(outputContent);
        }
        
        this.outputMessages.appendChild(outputDiv);
        
        if (resultObj.execution_output && agentConfig?.hasExecutionOutput) {
            this.appendExecutionOutput(resultObj, data);
        }
    }

    // 将执行结果输出到页面上，并在页面上显示执行结果的内容。如果执行结果类型是 writefile，则内容会显示 filepath。
    appendExecutionOutput(resultObj, data) {
        const execDiv = document.createElement('div');
        execDiv.className = 'message output-message execution-output';
        execDiv.style.marginTop = '4px';
        
        const agentType = data?.agent_type || resultObj.type;
        const agentConfig = Object.values(AGENT_TYPES).find(config => config.type === agentType);
        
        if (agentConfig) {
            execDiv.classList.add(agentConfig.type);
        }
        
        // Add execution output content with formatting for different file operations
        const execContent = document.createElement('span');
        if (resultObj.type === 'writefile') {
            execContent.textContent = `📝 Written Content:\n${resultObj.execution_output}\n`;
        } else if (resultObj.type === 'createfile') {
            execContent.textContent = `📁 ${resultObj.execution_output}\n`;
        } else {
            execContent.textContent = resultObj.execution_output;
        }
        execDiv.appendChild(execContent);
        
        this.outputMessages.appendChild(execDiv);
    }

    // 将代码输出添加到指定的HTML元素中，并使用Prism.js库对代码进行语法高亮
    appendCodeOutput(outputDiv, resultObj) {
        const codeBlock = document.createElement('div');
        codeBlock.className = 'code-block';
        
        const pre = document.createElement('pre');
        pre.className = 'line-numbers';
        
        const code = document.createElement('code');
        
        // 确定语言类型
        let language = 'bash';  // default for command
        if (resultObj.type === 'writefile' || resultObj.type === 'createfile') {
            // Extract file extension from execution_output
            const fileMatch = resultObj.execution_output.match(/(?:Created file|Created empty file at): .*\.(.*?)$/);
            if (fileMatch) {
                const ext = fileMatch[1].toLowerCase();
                language = fileExtensionToLanguageMap[ext] || 'plaintext';
            }
        }
        
        code.className = `language-${language}`;
        
        // Format command output to remove prompt
        code.textContent = resultObj.type === 'command' 
            ? resultObj.output.replace(/^\$ /, '')
            : resultObj.output;
        
        pre.appendChild(code);
        codeBlock.appendChild(pre);
        outputDiv.appendChild(codeBlock);
        
        // Trigger Prism to highlight the new code
        Prism.highlightElement(code);
    }


    // Existing utility methods
    appendMessage(content, type, data = null) {
        const messageDiv = this.createMessageElement(type, data);
        
        if (type === 'error') {
            messageDiv.textContent = content;
            this.chatMessages.appendChild(messageDiv);
            this.scrollToBottom(this.chatMessages);
            return;
        }

        const agentType = data?.agent_type;
        const agentConfig = agentType && Object.values(AGENT_TYPES).find(config => config.type === agentType);
        
        const targetPanel = agentConfig?.useOutputPanel
                            ? this.outputMessages 
                            : this.chatMessages;
        
        if (typeof content === 'string' && data?.type === 'json') {
            this.handleJsonContent(messageDiv, content, data, targetPanel);
        } else {
            this.appendSimpleContent(messageDiv, content, targetPanel);
        }
    }

    createMessageElement(type, data) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;
        
        const agentType = data?.agent_type;
        const agentConfig = agentType && Object.values(AGENT_TYPES).find(config => config.type === agentType);
        
        if (agentConfig?.useOutputPanel) {
            const badge = document.createElement('span');
            messageDiv.appendChild(badge);
            badge.className = `agent-badge ${agentConfig.type}`;
            badge.textContent = agentConfig.displayName;
        }
        return messageDiv;
    }

    scrollToBottom(panel) {
        panel.scrollTop = panel.scrollHeight;
    }

    /**
     * 执行代码并显示结果
     * 1. 禁用执行按钮并显示加载状态
     * 2. 发送代码到服务器执行
     * 3. 显示执行结果
     * 4. 恢复按钮状态
     */
    async executeCode(code, type, button) {
        // 1. 更新按钮状态为加载中
        button.disabled = true;
        button.innerHTML = 'Executing... <span class="loading"></span>';

        try {
            // 2. 发送代码到服务器执行
            const response = await fetch('/execute', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ code, type })
            });
            const result = await response.json();

            // 3. 显示执行结果
            const outputDiv = document.createElement('div');
            outputDiv.className = `output${result.output.includes('Error:') ? ' error' : ''}`;
            outputDiv.textContent = result.output;

            // 替换现有输出（如果有）
            const existingOutput = button.parentNode.querySelector('.output');
            if (existingOutput) existingOutput.remove();
            button.parentNode.appendChild(outputDiv);
        } catch (error) {
            this.appendMessage(`Error: ${error.message}`, 'error');
        } finally {
            // 4. 恢复按钮状态
            button.disabled = false;
            button.textContent = `Execute ${type} code`;
        }
    }

    // 添加 generate_code 生成的 python 代码 
    appendCodeBlock(code, type) {
        const codeBlock = document.createElement('div');
        codeBlock.className = 'code-block';

        // Create header
        const header = document.createElement('div');
        header.className = 'code-block-header';
        header.innerHTML = `
            <span>Code ${langMap[type].display}
                <span class="type-badge type-${type}">${type}</span>
            </span>
        `;
        codeBlock.appendChild(header);

        // Create code content
        const pre = document.createElement('pre');
        pre.className = 'line-numbers';
        
        const codeElement = document.createElement('code');
        // Use appropriate language class for Prism
        codeElement.className = `language-${langMap[type].language}`;
        codeElement.textContent = code;
        
        pre.appendChild(codeElement);
        codeBlock.appendChild(pre);

        // Create execute button
        const executeButton = document.createElement('button');
        executeButton.className = 'execute-button';
        executeButton.textContent = `Execute ${type} code`;
        executeButton.onclick = () => this.executeCode(code, type, executeButton);
        codeBlock.appendChild(executeButton);

        this.chatMessages.appendChild(codeBlock);
        this.scrollToBottom(this.chatMessages);

        // Trigger Prism to highlight the new code
        Prism.highlightElement(codeElement);
    }

    
}

// Initialize the app
document.addEventListener('DOMContentLoaded', () => {
    window.codeAssistant = new CodeAssistant();
});