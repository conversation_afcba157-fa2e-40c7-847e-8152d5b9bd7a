[schema]
description = "Views and analyzes file contents, handling large files by reading in chunks"
input = { file_path = "Path to the file to read" }
output = { type = "file_view", content = "Content of the file", success = "Whether operation succeeded", error = "Error message if failed (optional)" }
required_input = ["file_path"]
required_output = ["type", "success"]

[prompt]
content = '''
Read and analyze the following content from the file.
Return your analysis in a clear format.

File content:
{{ content }}

Current position: {{ current_position }}
Total rounds: {{ current_round }}/{{ max_rounds }}
'''
