{% extends "base.html" %}

{% block content %}
<div class="split-container">
    <!-- Output Panel -->
    <div class="panel" id="outputPanel">
        <div class="panel-header">
            Output & Execution Results
            <span id="taskStatus" class="task-status incomplete">Incomplete</span>
        </div>
        <div class="panel-content custom-scrollbar" id="outputMessages">
            <!-- Output messages will be appended here -->
        </div>
    </div>

    <!-- Resizer -->
    <div class="panel-divider" id="panelDivider"></div>

    <!-- Chat Panel -->
    <div class="panel" id="chatPanel">
        <div class="panel-header">
            Chat & Code Generation
        </div>
        <div class="panel-content custom-scrollbar" id="chatMessages">
            <div class="welcome-message text-gray-600 text-center p-4">
                <h2 class="text-xl font-bold mb-2">Welcome to Code Assistant!</h2>
                <p>Ask me to generate any Python code or shell commands.</p>
            </div>
        </div>
        <div class="input-area">
            <div class="input-container">
                <textarea 
                    id="userInput" 
                    placeholder="Type your command (Python or shell)..."
                ></textarea>
                <button id="sendButton">Send</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script type="module" src="{{ url_for('static', filename='js/main.js', v=timestamp) }}"></script>
{% endblock %}