import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")
    CLAUDE_MODEL = "claude-3-5-sonnet-20240620"
    ANTHROPIC_API_URL = "https://api.anthropic.com/v1/messages"
    MAX_EXECUTION_TIME = 10  # seconds
    COMMAND_TIMEOUT = 30  # seconds for shell command execution
    WORK_DIR = "/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_work_by_agent"
    
    # Commands that are safe to run in actual directory
    SAFE_COMMANDS = {
        'pwd',    # print working directory
        'ls',     # list directory contents
        'dir',    # list directory contents (Windows)
        'echo',   # print text
        'cat',    # view file contents
        'head',   # view start of file
        'tail',   # view end of file
        'wc',     # count lines/words/chars
        'grep',   # search text
        'find',   # search files
        'tree',   # show directory structure
        'which',  # show command location
        'type',   # show command type
        'mkdir',  # make directory
        'rmdir',  # remove directory
        'touch',  # create empty file
    }
