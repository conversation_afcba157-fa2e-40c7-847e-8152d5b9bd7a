<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Code Assistant{% endblock %}</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <!-- Update Prism theme and add plugins -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.css" rel="stylesheet">
    <style>
        /* Customize code block appearance */
        .code-block {
            margin: 8px 0;
            border-radius: 6px;
            overflow: hidden;
        }
        
        .code-block pre {
            margin: 0 !important;
            border-radius: 6px;
            background-color: #1a1a1a !important;
            padding: 1em !important;
        }
        
        .code-block code {
            font-family: 'JetBrains Mono', 'Fira Code', 'Menlo', monospace !important;
            font-size: 14px !important;
            line-height: 1.5 !important;
            background: none !important;
            text-shadow: none !important;
            padding: 0 !important;
        }
        
        /* Customize line numbers */
        .line-numbers .line-numbers-rows {
            border-right: 1px solid #404040 !important;
            padding: 1em 0 !important;
        }
        
        .line-numbers-rows > span:before {
            color: #606060 !important;
        }
    </style>
    <link href="{{ url_for('static', filename='css/styles.css') }}" rel="stylesheet">
</head>
<body class="bg-gray-100 min-h-screen">
    <nav class="bg-gray-800 text-white p-4">
        <div class="container mx-auto">
            <h1 class="text-xl font-bold">Code Assistant</h1>
        </div>
    </nav>

    <main class="container mx-auto p-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Update Prism.js and add plugins -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-bash.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-json.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-markup.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-css.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-markdown.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-yaml.min.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html>