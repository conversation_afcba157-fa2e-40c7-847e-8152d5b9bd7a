from typing import Dict, Any, Generator
from app.services.code_executor import CodeExecutor
from app.services.ai_service import ai_service
from app.services.prompt_manager import PromptManager
from .base import Agent
from app.core.sse import SSE

class CommandAgent(Agent):
    def __init__(self):
        super().__init__("CommandAgent")
        self.prompt_manager = PromptManager()
    
    async def run(self, command_request: str, os: str = "macOS", **kwargs) -> Dict[str, Any]:
        try:
            # Get prompt from prompt manager with provided OS
            prompt = self.prompt_manager.render_prompt(
                self.name, 
                command_request=command_request,
                os="macOS"
            )
            
            # Get command from Claude
            response = ai_service.ask_claude(prompt)
            
            # Extract command from response using parse_response_tags
            tags = self.prompt_manager.parse_response_tags(response)
            command = tags.get('command', '').strip()
            
            if not command:
                return {
                    "type": "command",
                    "output": "Failed to generate command",
                    "execution_output": None,
                    "success": False
                }

            return {
                "type": "command",
                "output": command,  # The command string
                "execution_output": None,
                "success": True
            }
            
        except Exception as e:
            return {
                "type": "command",
                "output": str(e),
                "execution_output": None,
                "success": False
            }