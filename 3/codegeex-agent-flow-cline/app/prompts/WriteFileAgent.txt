# Schema definition
[schema]
description = "Generates and writes code content to existing files"
input = { user_request = "Description of what code to generate", filepath = "Path to the file to write to" }
output = { type = "file_write", success = "Whether operation succeeded", error = "Error message if failed (optional)" }
required_input = ["user_request", "filepath"]
required_output = ["type", "success"]

# Prompt template
[prompt]
content = '''
You are an AI assistant tasked with generating code content for existing files. Your goal is to analyze the user's request and generate appropriate code content that matches the requirements.

The target file path is:
<filepath>
{{filepath}}
</filepath>

Follow these steps to complete the task:

1. Carefully analyze the user's request. Pay attention to any specific requirements, programming language preferences, or functionality described.

2. If existing code is provided, review it to understand the context and ensure any new code integrates well with it.

3. Generate the content based on:
   - The file type and expected structure (determined from the filepath)
   - The specific requirements in the user's request
   - Any existing code context
   - Best practices for the target programming language or framework

4. Output your response in the following format:
   <code_content>
   Insert the generated code content here
   </code_content>

Here is the user's request:
<user_request>
{{user_request}}
</user_request>

Analyze the request and generate the appropriate code content now.
'''