from typing import Dict, Any, Callable, Type
import asyncio
from app.agents.base import Agent

class AgentContext:
    """Handles the execution context for agents"""
    
    def __init__(self):
        self._create_agent = None
        self._agent_registry: Dict[str, Callable[..., Any]] = {}
        
    def set_create_agent(self, create_agent_func):
        """Set the create_agent function after initialization"""
        self._create_agent = create_agent_func
        
    def register_agent(self, name: str, func: Callable[..., Any]) -> None:
        """Register a new agent function in the context"""
        self._agent_registry[name] = func
        
    def unregister_agent(self, name: str) -> None:
        """Remove an agent function from the context"""
        if name in self._agent_registry:
            del self._agent_registry[name]
            
    def update_registry(self, registry: Dict[str, Type[Agent]]) -> None:
        """Update the agent registry with new agent types"""
        if not self._create_agent:
            raise RuntimeError("Agent context not properly initialized")
            
        # Clear existing registry
        self._agent_registry.clear()
        
        # Create wrapper functions for each agent type
        for agent_type, _ in registry.items():
            async def agent_wrapper(*args, agent_type=agent_type, **kwargs):
                agent = self._create_agent(agent_type)
                result = await agent.run(*args, **kwargs)
                
                # Ensure result has all required fields in unified format
                if not isinstance(result, dict):
                    result = {
                        "type": agent_type,
                        "output": str(result),
                        "execution_output": None,
                        "success": True
                    }
                else:
                    # Ensure all required fields are present
                    result.setdefault("type", agent_type)
                    result.setdefault("output", "")
                    result.setdefault("execution_output", None)
                    result.setdefault("success", True)
                    
                return result
            
            # Register the wrapper function
            self.register_agent(agent_type, agent_wrapper)
    
    async def run_agent(self, agent_type: str, *args, **kwargs) -> Any:
        """Run a specific agent by type"""
        if agent_type not in self._agent_registry:
            raise ValueError(f"Unknown agent type: {agent_type}")
            
        return await self._agent_registry[agent_type](*args, **kwargs)
    
    def get_context(self) -> Dict[str, Any]:
        """Get the execution context with all agent functions"""
        if not self._agent_registry:
            raise RuntimeError("No agents registered in context")
            
        context = {
            'asyncio': asyncio,
        }
        
        # Add all registered agent functions to context
        context.update(self._agent_registry)
        
        return context

# Create a singleton instance
agent_context = AgentContext()