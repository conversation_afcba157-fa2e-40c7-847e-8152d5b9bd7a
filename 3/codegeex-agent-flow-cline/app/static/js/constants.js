// Global language map
export const langMap = {
    python: { language: 'python', display: 'Python' },
    bash: { language: 'bash', display: 'Shell' }
};

// Extended language map for file extensions
export const fileExtensionToLanguageMap = {
    py: 'python',
    js: 'javascript',
    html: 'html',
    css: 'css',
    json: 'json',
    sh: 'bash',
    md: 'markdown',
    sql: 'sql',
    yml: 'yaml',
    yaml: 'yaml',
    xml: 'xml',
    java: 'java',
    cpp: 'cpp',
    c: 'c',
    cs: 'csharp',
    go: 'go',
    rs: 'rust',
    rb: 'ruby',
    php: 'php'
};

// Agent type configurations
export const AGENT_TYPES = {
    AI: {
        type: 'ai',
        useOutputPanel: false,
        hasCodeOutput: false,
        hasExecutionOutput: false,
        displayName: 'AI'
    },
    COMMAND: {
        type: 'command',
        useOutputPanel: true,
        hasCodeOutput: true,
        hasExecutionOutput: false,
        displayName: 'COMMAND',
        defaultLanguage: 'bash'
    },
    WRITE_FILE: {
        type: 'writefile',
        useOutputPanel: true,
        hasCodeOutput: true,
        hasExecutionOutput: true,
        displayName: 'WRITE FILE'
    },
    CREATE_FILE: {
        type: 'createfile',
        useOutputPanel: true,
        hasCodeOutput: false,
        hasExecutionOutput: true,
        displayName: 'CREATE FILE'
    },
    VIEW_FILE: {
        type: 'viewfile',
        useOutputPanel: true,
        hasCodeOutput: true,
        hasExecutionOutput: false,
        displayName: 'VIEW FILE'
    }
};
