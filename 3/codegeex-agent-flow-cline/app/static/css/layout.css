/* Modern Layout System */
.split-container {
    display: flex;
    gap: 1.5rem;
    height: calc(100vh - 80px);
    margin: -1.5rem;
    padding: 1.5rem;
    background: #f8fafc;
}

.panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.panel:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

.panel-header {
    padding: 1.25rem;
    background: linear-gradient(to right, #f8fafc, #ffffff);
    border-bottom: 1px solid #e2e8f0;
    border-radius: 16px 16px 0 0;
    font-weight: 600;
    color: #1a202c;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 1.25rem;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e0 #f8fafc;
}

.panel-divider {
    width: 6px;
    margin: 0 -0.75rem;
    background: linear-gradient(to bottom, #e2e8f0, #cbd5e0);
    cursor: col-resize;
    transition: background 0.3s ease;
    border-radius: 3px;
}

.panel-divider:hover {
    background: linear-gradient(to bottom, #cbd5e0, #a0aec0);
}

.input-area {
    position: sticky;
    bottom: 0;
    background: white;
    padding: 1.25rem;
    border-top: 1px solid #e2e8f0;
    border-radius: 0 0 16px 16px;
    box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.05);
}

/* Custom Scrollbar */
.panel-content::-webkit-scrollbar {
    width: 8px;
}

.panel-content::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 4px;
}

.panel-content::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 4px;
    border: 2px solid #f8fafc;
}

.panel-content::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

/* Responsive Layout */
@media (max-width: 768px) {
    .split-container {
        flex-direction: column;
        height: auto;
    }
    
    .panel-divider {
        height: 6px;
        width: auto;
        margin: 0.75rem 0;
    }
}
