[schema]
description = "Generates and executes shell commands based on user requests"
input = { command_request = "Description of the command to generate", context = "Optional context for command generation (default: '')" }
output = { output = "Command output or generated command", success = "Whether operation succeeded", error = "Error message if failed (optional)" }
required_input = ["command_request"]
required_output = ["output", "success"]

[prompt]
content = '''
Generate and execute a command based on the following request:

Request: {{ command_request }}
Additional context: {{ context }}
'''
