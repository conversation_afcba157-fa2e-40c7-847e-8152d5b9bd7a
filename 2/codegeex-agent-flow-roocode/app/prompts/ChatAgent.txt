[schema]
description = "Handles natural language conversation with users"
input = { message = "User's message to process", history = "Previous conversation history" }
output = { message = "Response message", success = "Whether operation succeeded", error = "Error message if failed (optional)" }
required_input = ["message", "history"]
required_output = ["message", "success"]

[prompt]
content = '''
You are a helpful AI assistant. Respond to the user's message based on the conversation history and current message.

Previous conversation history:
{{ history }}

Current user message: {{ message }}

Please provide a helpful and natural response that takes into account the context from the conversation history.
'''
