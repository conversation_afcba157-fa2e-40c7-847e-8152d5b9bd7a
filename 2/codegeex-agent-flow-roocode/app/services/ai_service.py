import json
import requests
from typing import Generator, Optional
from config import Config
from .prompt_manager import PromptManager

class AIService:
    def __init__(self):
        self.prompt_manager = PromptManager()

    def ask_claude(self, message: str) -> str:
        """Send a message to <PERSON> and get the response (non-streaming)"""
        headers = {
            "x-api-key": Config.ANTHROPIC_API_KEY,
            "anthropic-version": "2023-06-01",
            "content-type": "application/json"
        }
        data = {
            "model": Config.CLAUDE_MODEL,
            "max_tokens": 1024,
            "messages": [
                {"role": "user", "content": message}
            ]
        }
        response = requests.post(Config.ANTHROPIC_API_URL, headers=headers, json=data)
        try:
            reply = response.json()['content'][0]['text']
            return reply
        except Exception as e:
            print(f"Error in Claude response: {response.json()}")
            return None

    def ask_claude_stream(self, message: str) -> Generator[str, None, None]:
        """Send a message to <PERSON> and stream the response"""
        headers = {
            "x-api-key": Config.ANTHROPIC_API_KEY,
            "anthropic-version": "2023-06-01",
            "content-type": "application/json",
            "accept": "text/event-stream"
        }
        data = {
            "model": Config.CLAUDE_MODEL,
            "max_tokens": 1024,
            "messages": [
                {"role": "user", "content": message}
            ],
            "stream": True
        }

        try:
            response = requests.post(Config.ANTHROPIC_API_URL, headers=headers, json=data, stream=True)
            response.raise_for_status()
            
            for line in response.iter_lines():
                if not line or line.decode('utf-8').startswith(':'):  # Skip empty lines and comments
                    continue
                    
                line = line.decode('utf-8')
                if not line.startswith('data: '):
                    continue
                    
                data_str = line[6:]  # Remove 'data: ' prefix
                if data_str == '[DONE]':
                    break
                    
                try:
                    data = json.loads(data_str)
                    if data.get('type') == 'content_block_delta' and 'delta' in data:
                        text = data['delta'].get('text', '')
                        if text:
                            yield text
                except json.JSONDecodeError:
                    continue  # Skip invalid JSON
                    
        except requests.exceptions.RequestException as e:
            print(f"Request error: {e}")
            yield json.dumps({
                "error": str(e),
                "status": "error"
            })


# Create a singleton instance
ai_service = AIService()