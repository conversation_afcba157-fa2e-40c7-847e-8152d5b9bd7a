import json
from typing import Dict, Any, Generator
from app.services.ai_service import ai_service
from app.services.prompt_manager import Prompt<PERSON>anager
from app.agents import AGENT_REGISTRY

class CodeGenerator:
    def __init__(self):
        self.prompt_manager = PromptManager()
        # Create the agent documentation for the prompt
        self.agent_docs = self._generate_agent_docs()
        
    def _generate_agent_docs(self) -> str:
        """Generate documentation of available agents for the prompt"""
        agents_docs = {}
        for agent_name, agent_class in AGENT_REGISTRY.items():
            # Skip internal prompts (starting with '_')
            if agent_class.__name__.startswith('_'):
                continue
                
            # Get agent's schema from its prompt file
            try:
                schema = self.prompt_manager.get_schema(agent_class.__name__)
                params = {}
                
                # Get input parameters from schema
                for param_name, param_desc in schema.get('input', {}).items():
                    if isinstance(param_desc, str):
                        params[param_name] = {
                            "type": "str",
                            "description": param_desc,
                            "required": param_name in schema.get('required_input', [])
                        }
                
                # Add agent to docs
                agents_docs[agent_name] = {
                    "description": schema.get('description', 'No description available'),
                    "parameters": params
                }
            except Exception as e:
                print(f"Warning: Could not load schema for {agent_class.__name__}: {e}")
                continue
        
        return json.dumps(agents_docs, indent=2)
    
    def generate_code(self, user_input: str) -> Generator[Dict[str, Any], None, None]:
        """Generate code to handle the user's input using available agents"""
        prompt = self.prompt_manager.render_prompt('_CodeGenerator', 
            agent_docs=self.agent_docs,
            user_input=user_input
        )
            
        accumulated_response = ""
        
        for chunk in ai_service.ask_claude_stream(prompt):
            accumulated_response += chunk
            yield {
                'chunk': chunk,
                'status': 'streaming'
            }
        
        # Parse the complete response using tags
        try:
            parsed = self.prompt_manager.parse_response_tags(accumulated_response)
            if all(key in parsed for key in ['analysis', 'current_step', 'python_code', 'status']):
                # Wrap the code in an async function that returns the result
                code = f"""async def handle_request():
    response = {parsed['python_code']}
    return response
"""
                yield {
                    'type': 'python',
                    'code': code,
                    'analysis': parsed['analysis'],
                    'current_step': parsed['current_step'],
                    'status': 'complete'
                }
        except Exception as e:
            yield {
                'error': f'Error processing response: {str(e)}',
                'status': 'error'
            }

# Create a singleton instance
code_generator = CodeGenerator()