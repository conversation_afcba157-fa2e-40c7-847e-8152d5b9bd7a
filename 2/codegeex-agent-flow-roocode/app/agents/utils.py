from typing import Dict, Any, Type, List
from .base import Agent
from .file_agents import ViewFileAgent, WriteFileAgent
from .interaction_agents import Chat<PERSON>gent, CommandAgent

# Registry of available agents
AGENT_REGISTRY: Dict[str, Type[Agent]] = {
    # File operations
    "view_file": ViewFileAgent,  # View and analyze file contents
    "write_file": WriteFileAgent,  # Generate and write code files based on user requests
    
    # Interaction
    "chat": ChatAgent,  # Natural language conversation
    "command": CommandAgent  # Generate and execute shell commands
}

def create_agent(agent_type: str, **kwargs) -> Agent:
    """
    Create an agent instance by type
    
    Args:
        agent_type: Type of agent to create. Available types:
            - view_file: View and analyze file contents
            - write_file: Generate and write code files based on user requests
            - chat: Natural language conversation
            - command: Generate and execute shell commands
        **kwargs: Arguments to pass to agent constructor
        
    Returns:
        Instance of requested agent
        
    Raises:
        ValueError: If agent_type is not found in registry
    """
    if agent_type not in AGENT_REGISTRY:
        raise ValueError(f"Unknown agent type: {agent_type}")
        
    agent_class = AGENT_REGISTRY[agent_type]
    return agent_class(**kwargs)

async def run_agent_pipeline(agents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Run a pipeline of agents in sequence
    
    Args:
        agents: List of dicts containing agent configurations:
               [
                   {
                       "type": "agent_type",
                       "params": {param1: value1, ...},
                       "run_params": {run_param1: value1, ...}
                   },
                   ...
               ]
               
    Returns:
        List of results from each agent
    """
    results = []
    
    for agent_config in agents:
        agent_type = agent_config["type"]
        agent_params = agent_config.get("params", {})
        run_params = agent_config.get("run_params", {})
        
        # Create and run agent
        agent = create_agent(agent_type, **agent_params)
        result = await agent.run(**run_params)
        
        results.append({
            "agent_type": agent_type,
            "result": result
        })
        
    return results

def validate_agent_config(config: Dict[str, Any]) -> bool:
    """
    Validate agent configuration
    
    Args:
        config: Agent configuration dictionary
        
    Returns:
        True if configuration is valid, False otherwise
    """
    required_keys = {"type"}
    return all(key in config for key in required_keys)