import asyncio
import json
from flask import Blueprint, render_template, request, jsonify
from app.services.code_generator import code_generator
from app.services.code_executor import CodeExecutor
from app.agents import create_agent
from app.core.sse import SSE

main = Blueprint('main', __name__)

@main.route('/')
def home():
    return render_template('index.html')

@main.route('/ai/generate/stream', methods=['POST'])
def generate_code_stream():
    message = request.json.get('message', '')
    
    def generate():
        # Stream the code generation process
        for response in code_generator.generate_code(message):
            if response.get('status') == 'streaming':
                # 直接转发流式输出
                yield SSE.format_sse({
                    "type": "generation",
                    "chunk": response.get('chunk', ''),
                    "status": "streaming"
                })
            elif response.get('status') == 'complete':
                # 处理完整的响应
                yield SSE.format_sse({
                    "status": "complete",
                    "type": response.get('type'),
                    "code": response.get('code'),
                    "analysis": response.get('analysis'),
                    "current_step": response.get('current_step')
                })
                
                # 执行生成的代码
                try:
                    result = CodeExecutor.execute_python_code(response.get('code'))
                    yield SSE.format_sse({
                        "status": "execute",
                        "result": result
                    })
                except Exception as e:
                    yield SSE.format_sse({
                        "status": "error",
                        "error": str(e)
                    })
            elif response.get('status') == 'error':
                # 处理错误情况
                yield SSE.format_sse({
                    "status": "error",
                    "error": response.get('error')
                })
    
    return SSE.stream(generate())

@main.route('/execute', methods=['POST'])
def execute_code():
    code = request.json.get('code', '')
    command_type = request.json.get('type', 'python')
    
    try:
        if command_type == 'python':
            result = CodeExecutor.execute_python_code(code)
        else:
            result = CodeExecutor.execute_shell_command(code)
        
        return jsonify({
            'success': True,
            'output': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'output': f"Error: {str(e)}"
        })