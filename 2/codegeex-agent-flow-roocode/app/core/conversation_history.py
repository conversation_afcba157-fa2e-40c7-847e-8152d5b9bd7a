from typing import List, Dict, Any
from datetime import datetime
import json

class ConversationHistory:
    """管理对话历史的类，保留最近5轮对话"""
    
    def __init__(self, max_history: int = 5):
        """
        初始化对话历史管理器
        
        Args:
            max_history: 保留的最大对话轮数
        """
        self.max_history = max_history
        self.history: List[Dict[str, Any]] = []
    
    def add_message(self, role: str, content: str, timestamp: datetime = None):
        """
        添加一条消息到对话历史
        
        Args:
            role: 消息角色 ('user' 或 'assistant')
            content: 消息内容
            timestamp: 消息时间戳，默认为当前时间
        """
        if timestamp is None:
            timestamp = datetime.now()
            
        message = {
            "role": role,
            "content": content,
            "timestamp": timestamp.isoformat()
        }
        
        self.history.append(message)
        
        # 保持历史记录在最大限制内
        if len(self.history) > self.max_history * 2:  # 每轮对话包含用户和助手消息
            self.history = self.history[-self.max_history * 2:]
    
    def add_conversation_turn(self, user_message: str, assistant_message: str):
        """
        添加一轮完整的对话（用户消息+助手回复）
        
        Args:
            user_message: 用户消息
            assistant_message: 助手回复
        """
        self.add_message("user", user_message)
        self.add_message("assistant", assistant_message)
    
    def get_history(self) -> List[Dict[str, Any]]:
        """
        获取对话历史
        
        Returns:
            对话历史列表
        """
        return self.history.copy()
    
    def get_formatted_history(self) -> str:
        """
        获取格式化的对话历史，用于提示词
        
        Returns:
            格式化的对话历史字符串
        """
        if not self.history:
            return "No previous conversation history."
        
        formatted = []
        for msg in self.history:
            role_name = "User" if msg["role"] == "user" else "Assistant"
            formatted.append(f"{role_name}: {msg['content']}")
        
        return "\n".join(formatted)
    
    def clear(self):
        """清空对话历史"""
        self.history.clear()
    
    def get_last_user_message(self) -> str:
        """
        获取最后一条用户消息
        
        Returns:
            最后一条用户消息，如果没有则返回空字符串
        """
        for msg in reversed(self.history):
            if msg["role"] == "user":
                return msg["content"]
        return ""
    
    def get_conversation_turns(self) -> List[Dict[str, str]]:
        """
        获取对话轮次（用户消息+助手回复的配对）
        
        Returns:
            对话轮次列表
        """
        turns = []
        i = 0
        while i < len(self.history):
            if i + 1 < len(self.history) and self.history[i]["role"] == "user" and self.history[i + 1]["role"] == "assistant":
                turns.append({
                    "user": self.history[i]["content"],
                    "assistant": self.history[i + 1]["content"]
                })
                i += 2
            else:
                i += 1
        return turns
    
    def to_dict(self) -> Dict[str, Any]:
        """将对话历史转换为字典格式"""
        return {
            "max_history": self.max_history,
            "history": self.history
        }
    
    def from_dict(self, data: Dict[str, Any]):
        """从字典格式加载对话历史"""
        self.max_history = data.get("max_history", 5)
        self.history = data.get("history", [])
    
    def save_to_file(self, filepath: str):
        """将对话历史保存到文件"""
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)
    
    def load_from_file(self, filepath: str):
        """从文件加载对话历史"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            self.from_dict(data)
        except FileNotFoundError:
            # 文件不存在时使用空历史
            self.clear()


# 创建全局对话历史实例
conversation_history = ConversationHistory()