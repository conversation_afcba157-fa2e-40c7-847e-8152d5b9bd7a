[schema]
description = "Handles natural language conversation with users"
input = { message = "User's message to process" }
output = { message = "Response message", success = "Whether operation succeeded", error = "Error message if failed (optional)" }
required_input = ["message"]
required_output = ["message", "success"]

[prompt]
content = '''
Respond to the following user message in a helpful and natural way:

User message: {{ message }}
'''
