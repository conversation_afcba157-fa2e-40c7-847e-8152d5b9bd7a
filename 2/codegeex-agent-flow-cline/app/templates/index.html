{% extends "base.html" %}

{% block content %}
<div class="max-w-6xl mx-auto bg-white rounded-lg shadow-lg p-6">
    <!-- Chat History Section -->
    <div class="mb-6">
        <div class="flex justify-between items-center mb-3">
            <h3 class="text-lg font-semibold text-gray-700">Recent Conversations</h3>
            <button id="clearHistoryBtn" class="text-sm text-red-600 hover:text-red-800 transition-colors">
                Clear History
            </button>
        </div>
        <div id="chatHistory" class="space-y-2 max-h-40 overflow-y-auto p-3 border border-gray-200 rounded-lg bg-gray-50">
            <div class="text-gray-500 text-sm text-center py-2">No conversation history yet</div>
        </div>
    </div>

    <!-- Current Chat Section -->
    <div id="chatMessages" class="h-[70vh] overflow-y-auto mb-6 p-4 border border-gray-200 rounded-lg">
        <div class="welcome-message text-gray-600 text-center p-4">
            <h2 class="text-xl font-bold mb-2">Welcome to Code Assistant!</h2>
            <p>Ask me to generate any Python code or shell commands.</p>
        </div>
    </div>
    
    <div class="flex gap-4">
        <textarea 
            id="userInput" 
            class="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none h-24"
            placeholder="Type your command (Python or shell)..."
        ></textarea>
        <button 
            id="sendButton"
            class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors h-24"
        >
            Send
        </button>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/main.js') }}"></script>
{% endblock %}
