from typing import Dict, Any, Generator
from abc import ABC, abstractmethod

class Agent(ABC):
    def __init__(self, name: str):
        """
        Initialize base agent
        
        Args:
            name: Name of the agent
        """
        self.name = name
        self.state: Dict[str, Any] = {}  # Store agent state between runs
        
    @abstractmethod
    async def run(self, **kwargs) -> Dict[str, Any]:
        """
        Run the agent's main logic
        
        Args:
            **kwargs: Parameters needed for the agent's operation
            
        Returns:
            Dict containing the results of the agent's operation
        """
        pass