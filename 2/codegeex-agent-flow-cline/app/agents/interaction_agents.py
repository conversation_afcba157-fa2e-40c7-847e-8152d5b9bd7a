from typing import Dict, Any, Generator
from app.services.code_executor import CodeExecutor
from app.services.ai_service import ai_service
from app.services.prompt_manager import PromptManager
from .base import Agent

class ChatAgent(Agent):
    def __init__(self):
        super().__init__("ChatAgent")
        self.prompt_manager = PromptManager()
    
    async def run(self, message: str, **kwargs) -> Dict[str, Any]:
        # Get prompt from prompt manager
        prompt = self.prompt_manager.render_prompt(self.name, message=message)
        
        # Get response from <PERSON>
        response = ai_service.ask_claude(prompt)
                
        return {
            "type": "chat",
            "message": response,
            "success": True
        }

class CommandAgent(Agent):
    def __init__(self):
        super().__init__("CommandAgent")
        self.prompt_manager = PromptManager()
    
    async def run(self, command_request: str, context: str = "", **kwargs) -> Dict[str, Any]:
        # For direct command execution, we don't need to ask <PERSON>
        if command_request in ['pwd', 'ls', 'dir', 'whoami', 'date', 'echo']:
            result = CodeExecutor.execute_shell_command(command_request)
            command = command_request
        else:
            # Get prompt from prompt manager
            prompt = self.prompt_manager.render_prompt(self.name, command_request=command_request, context=context)
            
            # Get command from Claude
            command = ai_service.ask_claude(prompt)
            
            if not command:
                return {
                    "type": "command",
                    "success": False,
                    "error": "Failed to generate command"
                }
                
            # Execute the command
            result = CodeExecutor.execute_shell_command(command)
        
        return {
            "type": "command",
            "command": command_request,
            "output": result,
            "success": "Error:" not in result
        }