import os
from typing import Dict, Any, Optional, List
from .base import Agent
from app.services.prompt_manager import PromptManager
from app.services.ai_service import ai_service

class ViewFileAgent(Agent):
    def __init__(self, chunk_size: int = 1000, max_rounds: int = 5):
        """
        Initialize ViewFileAgent
        
        Args:
            chunk_size: Number of tokens to read per round
            max_rounds: Maximum number of rounds to read
        """
        super().__init__("ViewFileAgent")
        self.prompt_manager = PromptManager()
        self.chunk_size = chunk_size
        self.max_rounds = max_rounds
        self.state = {
            "current_position": 0,
            "current_round": 0,
            "file_path": None
        }

    async def run(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """
        Read and analyze a file
        
        Args:
            file_path: Path to the file to read
            **kwargs: Additional parameters
            
        Returns:
            Dict containing analysis results
        """
        if not os.path.exists(file_path):
            return {
                "type": "file_view",
                "success": False,
                "error": f"File not found: {file_path}"
            }
            
        self.state["file_path"] = file_path
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Get prompt from prompt manager
            prompt = self.prompt_manager.render_prompt(
                self.name,
                content=content,
                current_position=self.state["current_position"],
                current_round=self.state["current_round"],
                max_rounds=self.max_rounds
            )
            
            # Get analysis from Claude
            analysis = ai_service.ask_claude(prompt)
                    
            return {
                "type": "file_view",
                "file_path": file_path,
                "analysis": analysis,
                "success": True
            }
            
        except Exception as e:
            return {
                "type": "file_view",
                "success": False,
                "error": str(e)
            }

class WriteFileAgent(Agent):
    def __init__(self):
        """
        Initialize WriteFileAgent
        
        This agent generates code files based on user requests. It analyzes the request,
        determines an appropriate filename, and generates well-structured code content.
        The agent uses a template-based approach to ensure consistent output format.
        """
        super().__init__("WriteFileAgent")
        self.prompt_manager = PromptManager()
    
    async def run(self, 
                 user_request: str,
                 existing_code: str = "",
                 **kwargs) -> Dict[str, Any]:
        """
        Generate and write content to a file based on user request
        
        Args:
            user_request: Description of what code file to create
            existing_code: Optional existing code for context
            **kwargs: Additional parameters
            
        Returns:
            Dict containing operation results including:
            - type: Type of operation ("file_write")
            - file_path: Path where the file was written
            - success: Whether the operation succeeded
            - error: Error message if failed
        """
        try:
            # Get prompt from prompt manager
            prompt = self.prompt_manager.render_prompt(
                self.name,
                user_request=user_request,
                existing_code=existing_code or ""
            )
            
            # Get content from Claude
            response = ai_service.ask_claude(prompt)
            
            # Parse the response to get filepath and content
            parsed = self.prompt_manager.parse_response_tags(response)
            
            if not parsed.get("filepath") or not parsed.get("code_content"):
                return {
                    "type": "file_write",
                    "success": False,
                    "error": "Failed to parse response: missing filepath or code_content"
                }
            
            filepath = parsed["filepath"]
            content = parsed["code_content"]
            
            try:
                # Create directory if it doesn't exist
                directory = os.path.dirname(os.path.abspath(filepath))
                os.makedirs(directory, exist_ok=True)
                
                # Write the content to file
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
                return {
                    "type": "file_write",
                    "file_path": filepath,
                    "success": True
                }
            except OSError as e:
                return {
                    "type": "file_write",
                    "success": False,
                    "error": f"Failed to write file: {str(e)}"
                }
                
        except Exception as e:
            return {
                "type": "file_write",
                "success": False,
                "error": str(e)
            }