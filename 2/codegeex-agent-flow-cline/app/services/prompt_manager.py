import os
import re
import json
import tomli
from pathlib import Path
from jinja2 import Template, Environment, meta, TemplateError
from typing import Dict, Any, Optional, Set, Tuple

class PromptManager:
    def __init__(self):
        self.prompts_dir = Path(__file__).parent.parent / 'prompts'
        self._cache: Dict[str, Dict[str, Any]] = {}
        
    def _load_prompt(self, prompt_name: str) -> Dict[str, Any]:
        """
        Load a prompt and its schema from file, using cache if available
        
        Returns:
            Dict containing 'schema' and 'prompt' sections
        """
        if prompt_name in self._cache:
            return self._cache[prompt_name]
            
        prompt_path = self.prompts_dir / f"{prompt_name}.txt"
        if not prompt_path.exists():
            raise FileNotFoundError(f"Prompt file not found: {prompt_path}")
            
        with open(prompt_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        try:
            # Parse TOML content
            data = tomli.loads(content)
            
            # Validate required sections
            if 'schema' not in data:
                raise ValueError(f"Missing 'schema' section in {prompt_name}")
            if 'prompt' not in data:
                raise ValueError(f"Missing 'prompt' section in {prompt_name}")
            if 'content' not in data['prompt']:
                raise ValueError(f"Missing 'content' in prompt section in {prompt_name}")
                
            self._cache[prompt_name] = data
            return data
            
        except tomli.TOMLDecodeError as e:
            raise ValueError(f"Invalid TOML format in {prompt_name}: {str(e)}")
    
    def get_schema(self, prompt_name: str) -> Dict[str, Any]:
        """Get the schema section of a prompt"""
        data = self._load_prompt(prompt_name)
        return data['schema']
    
    def render_prompt(self, prompt_name: str, **kwargs: Any) -> str:
        """
        Render a prompt template with given variables
        
        Args:
            prompt_name: Name of the prompt file (without .txt extension)
            **kwargs: Variables to be used in the template
            
        Returns:
            Rendered prompt string
        
        Raises:
            FileNotFoundError: If prompt file doesn't exist
            TemplateError: If there's an error in template syntax
            KeyError: If required variables are missing
        """
        try:
            # Load template string
            data = self._load_prompt(prompt_name)
            template_str = data['prompt']['content']
            
            # Create Jinja2 Environment
            env = Environment()
            
            # Parse the template to get required variables
            ast = env.parse(template_str)
            required_vars: Set[str] = meta.find_undeclared_variables(ast)
            
            # Check if all required variables are provided
            missing_vars = required_vars - set(kwargs.keys())
            if missing_vars:
                raise KeyError(f"Missing required template variables: {', '.join(missing_vars)}")
            
            # Create and render the template
            template = Template(template_str)
            return template.render(**kwargs)
            
        except TemplateError as e:
            raise TemplateError(f"Template error in {prompt_name}: {str(e)}")
        except Exception as e:
            raise Exception(f"Error rendering prompt {prompt_name}: {str(e)}")
            
    def parse_response_tags(self, response: str) -> Dict[str, Any]:
        """
        Parse XML-like tags in the response and return as JSON
        
        Args:
            response: Response string containing XML-like tags
            
        Returns:
            Dictionary containing the parsed tag contents
            
        Example:
            Input:
                <filepath>/path/to/file.py</filepath>
                <code_content>
                print("Hello")
                </code_content>
                
            Output:
                {
                    "filepath": "/path/to/file.py",
                    "code_content": 'print("Hello")'
                }
        """
        result = {}
        
        # Find all XML-like tags and their content
        pattern = r'<(\w+)>([\s\S]*?)</\1>'
        matches = re.finditer(pattern, response)
        
        for match in matches:
            tag_name = match.group(1)
            content = match.group(2).strip()
            result[tag_name] = content
            
        return result