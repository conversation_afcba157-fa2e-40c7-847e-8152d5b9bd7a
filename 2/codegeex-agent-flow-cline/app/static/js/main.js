class CodeAssistant {
    constructor() {
        this.chatMessages = document.getElementById('chatMessages');
        this.userInput = document.getElementById('userInput');
        this.chatHistory = document.getElementById('chatHistory');
        this.clearHistoryBtn = document.getElementById('clearHistoryBtn');
        this.currentConversation = [];
        this.conversationHistory = this.loadConversationHistory();
        this.setupEventListeners();
        this.renderChatHistory();
    }

    setupEventListeners() {
        this.userInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        document.getElementById('sendButton').addEventListener('click', () => this.sendMessage());
        this.clearHistoryBtn.addEventListener('click', () => this.clearHistory());
    }

    async sendMessage() {
        const message = this.userInput.value.trim();
        if (!message) return;

        this.userInput.value = '';
        this.appendMessage(message, 'user');
        
        // Add user message to current conversation
        this.currentConversation.push({
            type: 'user',
            content: message,
            timestamp: new Date().toISOString()
        });

        try {
            // Create loading message
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'message ai-message loading';
            loadingDiv.textContent = 'Assistant: ';
            const streamingContent = document.createElement('span');
            loadingDiv.appendChild(streamingContent);
            this.chatMessages.appendChild(loadingDiv);
            this.scrollToBottom();

            // Setup SSE
            const response = await fetch('/ai/generate/stream', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message })
            });

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let accumulatedData = '';

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                // Decode the chunk and split by double newlines (SSE format)
                const chunk = decoder.decode(value);
                const events = chunk.split('\n\n');

                for (const event of events) {
                    if (!event.trim()) continue;

                    // Parse the SSE data
                    const dataMatch = event.match(/data: (.+)$/m);
                    if (!dataMatch) continue;

                    try {
                        const data = JSON.parse(dataMatch[1]);
                        
                        if (data.status === 'streaming') {
                            // Update the streaming content
                            streamingContent.textContent += data.chunk;
                            this.scrollToBottom();
                        } else if (data.status === 'complete' || data.status === 'execute') {
                            // Remove the loading message
                            loadingDiv.remove();
                            
                            if (data.status === 'complete') {
                                // Show the analysis and current step
                                this.appendMessage(data.analysis, 'ai');
                                this.appendMessage(`Current Step: ${data.current_step}`, 'ai');
                                
                                // Add AI response to current conversation
                                this.currentConversation.push({
                                    type: 'ai',
                                    content: `${data.analysis}\nCurrent Step: ${data.current_step}`,
                                    timestamp: new Date().toISOString()
                                });
                                
                                // Show the generated code
                                if (data.code) {
                                    this.appendCodeBlock(data.code, data.type);
                                    // Add code to conversation
                                    this.currentConversation.push({
                                        type: 'code',
                                        content: data.code,
                                        codeType: data.type,
                                        timestamp: new Date().toISOString()
                                    });
                                }
                            } else if (data.status === 'execute') {
                                // Show execution results
                                if (data.result) {
                                    const result = typeof data.result === 'string' 
                                        ? data.result 
                                        : JSON.stringify(data.result, null, 2);
                                    this.appendMessage(`Output: ${result}`, 'output');
                                    // Add execution result to conversation
                                    this.currentConversation.push({
                                        type: 'output',
                                        content: result,
                                        timestamp: new Date().toISOString()
                                    });
                                }
                            }
                            
                            // Save conversation to history after complete response
                            this.saveConversation();
                        } else if (data.status === 'error') {
                            loadingDiv.remove();
                            this.appendMessage(`Error: ${data.error}`, 'error');
                        }
                    } catch (error) {
                        console.error('Error parsing SSE data:', error);
                        loadingDiv.remove();
                        this.appendMessage(`Error: ${error.message}`, 'error');
                    }
                }
            }
        } catch (error) {
            this.appendMessage(`Error: ${error.message}`, 'error');
        }
    }

    appendMessage(message, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;
        
        if (type === 'user') {
            messageDiv.textContent = `You: ${message}`;
        } else if (type === 'ai') {
            messageDiv.textContent = `Assistant: ${message}`;
        } else {
            messageDiv.textContent = message;
        }
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    appendCodeBlock(code, type) {
        const codeBlock = document.createElement('div');
        codeBlock.className = 'code-block';

        // Create header
        const header = document.createElement('div');
        header.className = 'code-block-header';
        header.innerHTML = `
            <span>Code ${type === 'python' ? 'Python' : 'Shell'}
                <span class="type-badge type-${type}">${type}</span>
            </span>
        `;
        codeBlock.appendChild(header);

        // Create code content
        const pre = document.createElement('pre');
        const codeElement = document.createElement('code');
        codeElement.className = `language-${type === 'python' ? 'python' : 'bash'}`;
        codeElement.textContent = code;
        pre.appendChild(codeElement);
        codeBlock.appendChild(pre);

        // Create execute button
        const executeButton = document.createElement('button');
        executeButton.className = 'execute-button';
        executeButton.textContent = `Execute ${type} code`;
        executeButton.onclick = () => this.executeCode(code, type, executeButton);
        codeBlock.appendChild(executeButton);

        this.chatMessages.appendChild(codeBlock);
        this.scrollToBottom();

        // Highlight code
        Prism.highlightElement(codeElement);
    }

    async executeCode(code, type, button) {
        button.disabled = true;
        button.innerHTML = 'Executing... <span class="loading"></span>';

        try {
            const response = await fetch('/execute', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ code, type })
            });

            const result = await response.json();
            
            // Create output display
            const outputDiv = document.createElement('div');
            outputDiv.className = `output${result.output.includes('Error:') ? ' error' : ''}`;
            outputDiv.textContent = result.output;
            
            // Remove any existing output
            const existingOutput = button.parentNode.querySelector('.output');
            if (existingOutput) {
                existingOutput.remove();
            }
            
            button.parentNode.appendChild(outputDiv);
        } catch (error) {
            this.appendMessage(`Error: ${error.message}`, 'error');
        } finally {
            button.disabled = false;
            button.textContent = `Execute ${type} code`;
        }
    }

    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    loadConversationHistory() {
        try {
            const saved = localStorage.getItem('codeAssistantHistory');
            return saved ? JSON.parse(saved) : [];
        } catch (error) {
            console.error('Error loading conversation history:', error);
            return [];
        }
    }

    saveConversation() {
        if (this.currentConversation.length === 0) return;

        // Add current conversation to history
        this.conversationHistory.unshift({
            id: Date.now(),
            messages: [...this.currentConversation],
            timestamp: new Date().toISOString()
        });

        // Keep only the last 5 conversations
        this.conversationHistory = this.conversationHistory.slice(0, 5);

        // Save to localStorage
        try {
            localStorage.setItem('codeAssistantHistory', JSON.stringify(this.conversationHistory));
        } catch (error) {
            console.error('Error saving conversation history:', error);
        }

        // Clear current conversation
        this.currentConversation = [];

        // Update history display
        this.renderChatHistory();
    }

    renderChatHistory() {
        if (this.conversationHistory.length === 0) {
            this.chatHistory.innerHTML = '<div class="text-gray-500 text-sm text-center py-2">No conversation history yet</div>';
            return;
        }

        this.chatHistory.innerHTML = '';
        this.conversationHistory.forEach((conversation, index) => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item p-2 border border-gray-200 rounded cursor-pointer hover:bg-gray-100 transition-colors';
            
            const firstUserMessage = conversation.messages.find(msg => msg.type === 'user');
            const preview = firstUserMessage ? firstUserMessage.content.substring(0, 50) + (firstUserMessage.content.length > 50 ? '...' : '') : 'Empty conversation';
            const date = new Date(conversation.timestamp).toLocaleDateString();
            
            historyItem.innerHTML = `
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="text-sm font-medium text-gray-800">${preview}</div>
                        <div class="text-xs text-gray-500">${date}</div>
                    </div>
                    <button class="delete-history text-red-500 hover:text-red-700 text-xs ml-2" data-index="${index}">
                        ×
                    </button>
                </div>
            `;

            // Add click event to load conversation
            historyItem.addEventListener('click', (e) => {
                if (!e.target.classList.contains('delete-history')) {
                    this.loadConversation(conversation);
                }
            });

            // Add delete event
            const deleteBtn = historyItem.querySelector('.delete-history');
            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.deleteConversation(index);
            });

            this.chatHistory.appendChild(historyItem);
        });
    }

    loadConversation(conversation) {
        // Clear current chat
        this.chatMessages.innerHTML = '';
        
        // Load conversation messages
        conversation.messages.forEach(message => {
            if (message.type === 'user') {
                this.appendMessage(message.content, 'user');
            } else if (message.type === 'ai') {
                this.appendMessage(message.content, 'ai');
            } else if (message.type === 'code') {
                this.appendCodeBlock(message.content, message.codeType);
            } else if (message.type === 'output') {
                this.appendMessage(`Output: ${message.content}`, 'output');
            }
        });
    }

    deleteConversation(index) {
        this.conversationHistory.splice(index, 1);
        try {
            localStorage.setItem('codeAssistantHistory', JSON.stringify(this.conversationHistory));
        } catch (error) {
            console.error('Error saving conversation history:', error);
        }
        this.renderChatHistory();
    }

    clearHistory() {
        this.conversationHistory = [];
        try {
            localStorage.removeItem('codeAssistantHistory');
        } catch (error) {
            console.error('Error clearing conversation history:', error);
        }
        this.renderChatHistory();
    }
}

// Initialize the app
document.addEventListener('DOMContentLoaded', () => {
    window.codeAssistant = new CodeAssistant();
});
