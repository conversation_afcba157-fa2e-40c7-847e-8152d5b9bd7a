from typing import Dict, Any
import asyncio

class AgentContext:
    """Handles the execution context for agents"""
    
    def __init__(self):
        self._create_agent = None
    
    def set_create_agent(self, create_agent_func):
        """Set the create_agent function after initialization"""
        self._create_agent = create_agent_func
    
    async def chat(self, message: str) -> str:
        """Chat agent helper function"""
        if not self._create_agent:
            raise RuntimeError("Agent context not properly initialized")
            
        agent = self._create_agent("chat")
        result = await agent.run(message=message)
        return result.get('message', '')

    async def command(self, command_request: str, context: str = '') -> str:
        """Command agent helper function"""
        if not self._create_agent:
            raise RuntimeError("Agent context not properly initialized")
            
        agent = self._create_agent("command")
        result = await agent.run(command_request=command_request, context=context)
        return result.get('output', '')

    async def viewfile(self, path: str) -> Dict[str, Any]:
        """View file agent helper function"""
        if not self._create_agent:
            raise RuntimeError("Agent context not properly initialized")
            
        agent = self._create_agent("view_file")
        return await agent.run(file_path=path)

    async def writefile(self, user_request: str, existing_code: str = '') -> Dict[str, Any]:
        """Write file agent helper function"""
        if not self._create_agent:
            raise RuntimeError("Agent context not properly initialized")
            
        agent = self._create_agent("write_file")
        return await agent.run(
            user_request=user_request,
            existing_code=existing_code
        )

    def get_context(self) -> Dict[str, Any]:
        """Get the execution context with all agent functions"""
        return {
            'asyncio': asyncio,
            'chat': self.chat,
            'command': self.command,
            'viewfile': self.viewfile,
            'writefile': self.writefile
        }

# Create a singleton instance
agent_context = AgentContext()