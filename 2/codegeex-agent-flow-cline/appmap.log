[2024-12-19 18:29:17,770] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-19 18:29:17,771] INFO _appmap.configuration: config: []
[2024-12-19 18:29:17,771] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/pip'})
[2024-12-19 18:29:17,783] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:29:27,754] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-19 18:29:27,755] INFO _appmap.configuration: config: []
[2024-12-19 18:29:27,755] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-19 18:29:27,759] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:29:27,962] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:35:00,408] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-19 18:35:00,409] INFO _appmap.configuration: config: []
[2024-12-19 18:35:00,409] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-19 18:35:00,413] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:35:00,627] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:35:36,152] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:36:06,072] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:36:31,395] INFO _appmap.env: appmap enabled: False
[2024-12-19 19:47:29,982] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-19 19:47:29,983] INFO _appmap.configuration: config: []
[2024-12-19 19:47:29,983] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-19 19:47:29,994] INFO _appmap.env: appmap enabled: False
[2024-12-19 19:47:30,208] INFO _appmap.env: appmap enabled: False
[2024-12-19 19:47:57,011] INFO _appmap.env: appmap enabled: False
[2024-12-19 19:48:28,106] INFO _appmap.env: appmap enabled: False
[2024-12-19 20:06:51,272] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-19 20:06:51,273] INFO _appmap.configuration: config: []
[2024-12-19 20:06:51,273] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-19 20:06:51,288] INFO _appmap.env: appmap enabled: False
[2024-12-19 20:06:51,509] INFO _appmap.env: appmap enabled: False
[2024-12-19 20:07:11,979] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:37:47,732] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 10:37:47,732] INFO _appmap.configuration: config: []
[2024-12-20 10:37:47,732] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 10:37:47,745] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:38:23,162] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 10:38:23,162] INFO _appmap.configuration: config: []
[2024-12-20 10:38:23,162] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 10:38:23,176] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:38:23,395] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:44:51,377] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:46:27,778] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 10:46:27,778] INFO _appmap.configuration: config: []
[2024-12-20 10:46:27,778] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 10:46:27,793] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:46:28,022] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:50:17,545] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 10:50:17,545] INFO _appmap.configuration: config: []
[2024-12-20 10:50:17,545] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 10:50:17,560] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:50:17,785] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:11:15,746] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 11:11:15,746] INFO _appmap.configuration: config: []
[2024-12-20 11:11:15,746] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 11:11:15,760] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:11:15,975] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:43:03,364] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 11:43:03,364] INFO _appmap.configuration: config: []
[2024-12-20 11:43:03,364] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 11:43:03,377] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:43:03,740] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:46:37,351] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 11:46:37,351] INFO _appmap.configuration: config: []
[2024-12-20 11:46:37,352] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 11:46:37,358] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:46:37,581] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:59:09,323] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 11:59:09,323] INFO _appmap.configuration: config: []
[2024-12-20 11:59:09,323] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 11:59:09,328] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:59:09,566] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:47:48,136] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 12:47:48,136] INFO _appmap.configuration: config: []
[2024-12-20 12:47:48,136] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 12:47:48,202] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:47:48,496] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:52:52,908] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 12:52:52,908] INFO _appmap.configuration: config: []
[2024-12-20 12:52:52,908] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 12:52:52,923] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:52:53,171] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:55:56,280] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:56:51,854] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 12:56:51,854] INFO _appmap.configuration: config: []
[2024-12-20 12:56:51,854] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 12:56:51,866] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:59:27,853] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 12:59:27,853] INFO _appmap.configuration: config: []
[2024-12-20 12:59:27,853] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 12:59:27,865] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:59:28,094] INFO _appmap.env: appmap enabled: False
[2024-12-20 13:01:01,432] INFO _appmap.env: appmap enabled: False
[2024-12-20 13:01:05,122] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 13:01:05,122] INFO _appmap.configuration: config: []
[2024-12-20 13:01:05,122] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 13:01:05,127] INFO _appmap.env: appmap enabled: False
[2024-12-20 13:01:05,304] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:10:41,704] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:14:49,062] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:14:49,062] INFO _appmap.configuration: config: []
[2024-12-20 14:14:49,062] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:14:49,076] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:14:49,321] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:17:03,853] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:17:03,854] INFO _appmap.configuration: config: []
[2024-12-20 14:17:03,854] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:17:03,868] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:17:04,094] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:22:37,471] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:22:37,471] INFO _appmap.configuration: config: []
[2024-12-20 14:22:37,471] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:22:37,483] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:22:37,701] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:24:45,751] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:25:10,166] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:25:10,166] INFO _appmap.configuration: config: []
[2024-12-20 14:25:10,166] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:25:10,172] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:25:10,378] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:27:27,593] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:27:34,420] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:27:34,420] INFO _appmap.configuration: config: []
[2024-12-20 14:27:34,420] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:27:34,426] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:27:34,598] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:31:29,248] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:31:29,248] INFO _appmap.configuration: config: []
[2024-12-20 14:31:29,248] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:31:29,265] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:31:29,523] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:33:31,293] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:33:31,293] INFO _appmap.configuration: config: []
[2024-12-20 14:33:31,293] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:33:31,308] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:33:31,546] INFO _appmap.env: appmap enabled: False
[2024-12-22 10:57:04,119] INFO _appmap.env: appmap enabled: False
[2024-12-22 10:57:04,396] INFO _appmap.env: appmap enabled: False
[2024-12-22 11:12:12,566] INFO _appmap.env: appmap enabled: False
[2024-12-22 11:12:12,806] INFO _appmap.env: appmap enabled: False
[2024-12-22 12:40:38,498] INFO _appmap.env: appmap enabled: False
[2024-12-22 12:40:38,736] INFO _appmap.env: appmap enabled: False
[2024-12-22 12:41:45,608] INFO _appmap.env: appmap enabled: False
[2024-12-22 12:41:45,841] INFO _appmap.env: appmap enabled: False
[2024-12-22 12:55:49,676] INFO _appmap.env: appmap enabled: False
[2024-12-23 11:50:32,770] INFO _appmap.env: appmap enabled: False
[2024-12-23 11:50:33,028] INFO _appmap.env: appmap enabled: False
[2024-12-23 12:10:54,358] INFO _appmap.env: appmap enabled: False
[2024-12-23 12:10:54,574] INFO _appmap.env: appmap enabled: False
[2024-12-23 15:45:49,140] INFO _appmap.env: appmap enabled: False
[2024-12-23 15:45:49,409] INFO _appmap.env: appmap enabled: False
[2024-12-23 15:47:55,036] INFO _appmap.env: appmap enabled: False
[2024-12-23 15:47:55,259] INFO _appmap.env: appmap enabled: False
[2024-12-23 15:56:17,884] INFO _appmap.env: appmap enabled: False
[2024-12-23 15:56:18,104] INFO _appmap.env: appmap enabled: False
[2024-12-23 16:03:55,715] INFO _appmap.env: appmap enabled: False
[2024-12-23 16:03:55,949] INFO _appmap.env: appmap enabled: False
[2024-12-23 16:40:36,993] INFO _appmap.env: appmap enabled: False
[2024-12-23 16:40:37,236] INFO _appmap.env: appmap enabled: False
[2024-12-23 16:42:50,384] INFO _appmap.env: appmap enabled: False
[2024-12-23 16:42:50,606] INFO _appmap.env: appmap enabled: False
[2024-12-23 16:46:48,224] INFO _appmap.env: appmap enabled: False
[2024-12-23 16:46:48,441] INFO _appmap.env: appmap enabled: False
[2024-12-23 16:50:52,228] INFO _appmap.env: appmap enabled: False
[2024-12-23 18:06:19,002] INFO _appmap.env: appmap enabled: False
[2024-12-23 18:06:26,146] INFO _appmap.env: appmap enabled: False
[2024-12-23 18:08:20,618] INFO _appmap.env: appmap enabled: False
[2024-12-23 18:08:23,151] INFO _appmap.env: appmap enabled: False
[2024-12-23 18:47:18,301] INFO _appmap.env: appmap enabled: False
[2024-12-23 18:47:18,559] INFO _appmap.env: appmap enabled: False
[2024-12-23 20:55:36,610] INFO _appmap.env: appmap enabled: False
[2024-12-23 20:55:36,854] INFO _appmap.env: appmap enabled: False
[2024-12-23 21:08:09,160] INFO _appmap.env: appmap enabled: False
[2024-12-23 21:08:09,382] INFO _appmap.env: appmap enabled: False
[2024-12-23 21:17:13,419] INFO _appmap.env: appmap enabled: False
[2024-12-23 21:17:13,643] INFO _appmap.env: appmap enabled: False
[2024-12-23 21:17:48,732] INFO _appmap.env: appmap enabled: False
[2024-12-23 21:17:48,915] INFO _appmap.env: appmap enabled: False
[2024-12-23 21:18:31,614] INFO _appmap.env: appmap enabled: False
[2024-12-23 21:18:31,833] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:28:04,851] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:28:05,095] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:29:11,711] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:31:23,441] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:31:23,660] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:33:34,101] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:33:34,319] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:36:54,321] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:36:54,541] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:38:50,160] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:38:50,380] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:23:39,170] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:23:39,465] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:25:14,962] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:25:15,181] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:30:30,627] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:30:30,870] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:31:17,637] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:31:17,816] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:37:24,727] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:37:24,945] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:40:03,438] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:52:48,850] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:52:49,074] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:56:00,650] INFO _appmap.env: appmap enabled: False
[2024-12-24 15:30:29,939] INFO _appmap.env: appmap enabled: False
[2024-12-24 15:30:30,184] INFO _appmap.env: appmap enabled: False
