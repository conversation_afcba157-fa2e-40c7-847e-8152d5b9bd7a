[2024-12-19 18:29:17,770] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-19 18:29:17,771] INFO _appmap.configuration: config: []
[2024-12-19 18:29:17,771] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/pip'})
[2024-12-19 18:29:17,783] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:29:27,754] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-19 18:29:27,755] INFO _appmap.configuration: config: []
[2024-12-19 18:29:27,755] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-19 18:29:27,759] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:29:27,962] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:35:00,408] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-19 18:35:00,409] INFO _appmap.configuration: config: []
[2024-12-19 18:35:00,409] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-19 18:35:00,413] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:35:00,627] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:35:36,152] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:36:06,072] INFO _appmap.env: appmap enabled: False
[2024-12-19 18:36:31,395] INFO _appmap.env: appmap enabled: False
[2024-12-19 19:47:29,982] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-19 19:47:29,983] INFO _appmap.configuration: config: []
[2024-12-19 19:47:29,983] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-19 19:47:29,994] INFO _appmap.env: appmap enabled: False
[2024-12-19 19:47:30,208] INFO _appmap.env: appmap enabled: False
[2024-12-19 19:47:57,011] INFO _appmap.env: appmap enabled: False
[2024-12-19 19:48:28,106] INFO _appmap.env: appmap enabled: False
[2024-12-19 20:06:51,272] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-19 20:06:51,273] INFO _appmap.configuration: config: []
[2024-12-19 20:06:51,273] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-19 20:06:51,288] INFO _appmap.env: appmap enabled: False
[2024-12-19 20:06:51,509] INFO _appmap.env: appmap enabled: False
[2024-12-19 20:07:11,979] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:37:47,732] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 10:37:47,732] INFO _appmap.configuration: config: []
[2024-12-20 10:37:47,732] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 10:37:47,745] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:38:23,162] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 10:38:23,162] INFO _appmap.configuration: config: []
[2024-12-20 10:38:23,162] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 10:38:23,176] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:38:23,395] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:44:51,377] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:46:27,778] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 10:46:27,778] INFO _appmap.configuration: config: []
[2024-12-20 10:46:27,778] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 10:46:27,793] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:46:28,022] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:50:17,545] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 10:50:17,545] INFO _appmap.configuration: config: []
[2024-12-20 10:50:17,545] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 10:50:17,560] INFO _appmap.env: appmap enabled: False
[2024-12-20 10:50:17,785] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:11:15,746] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 11:11:15,746] INFO _appmap.configuration: config: []
[2024-12-20 11:11:15,746] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 11:11:15,760] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:11:15,975] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:43:03,364] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 11:43:03,364] INFO _appmap.configuration: config: []
[2024-12-20 11:43:03,364] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 11:43:03,377] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:43:03,740] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:46:37,351] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 11:46:37,351] INFO _appmap.configuration: config: []
[2024-12-20 11:46:37,352] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 11:46:37,358] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:46:37,581] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:59:09,323] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 11:59:09,323] INFO _appmap.configuration: config: []
[2024-12-20 11:59:09,323] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 11:59:09,328] INFO _appmap.env: appmap enabled: False
[2024-12-20 11:59:09,566] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:47:48,136] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 12:47:48,136] INFO _appmap.configuration: config: []
[2024-12-20 12:47:48,136] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 12:47:48,202] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:47:48,496] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:52:52,908] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 12:52:52,908] INFO _appmap.configuration: config: []
[2024-12-20 12:52:52,908] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 12:52:52,923] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:52:53,171] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:55:56,280] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:56:51,854] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 12:56:51,854] INFO _appmap.configuration: config: []
[2024-12-20 12:56:51,854] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 12:56:51,866] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:59:27,853] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 12:59:27,853] INFO _appmap.configuration: config: []
[2024-12-20 12:59:27,853] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 12:59:27,865] INFO _appmap.env: appmap enabled: False
[2024-12-20 12:59:28,094] INFO _appmap.env: appmap enabled: False
[2024-12-20 13:01:01,432] INFO _appmap.env: appmap enabled: False
[2024-12-20 13:01:05,122] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 13:01:05,122] INFO _appmap.configuration: config: []
[2024-12-20 13:01:05,122] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 13:01:05,127] INFO _appmap.env: appmap enabled: False
[2024-12-20 13:01:05,304] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:10:41,704] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:14:49,062] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:14:49,062] INFO _appmap.configuration: config: []
[2024-12-20 14:14:49,062] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:14:49,076] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:14:49,321] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:17:03,853] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:17:03,854] INFO _appmap.configuration: config: []
[2024-12-20 14:17:03,854] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:17:03,868] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:17:04,094] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:22:37,471] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:22:37,471] INFO _appmap.configuration: config: []
[2024-12-20 14:22:37,471] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:22:37,483] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:22:37,701] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:24:45,751] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:25:10,166] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:25:10,166] INFO _appmap.configuration: config: []
[2024-12-20 14:25:10,166] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:25:10,172] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:25:10,378] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:27:27,593] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:27:34,420] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:27:34,420] INFO _appmap.configuration: config: []
[2024-12-20 14:27:34,420] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:27:34,426] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:27:34,598] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:31:29,248] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:31:29,248] INFO _appmap.configuration: config: []
[2024-12-20 14:31:29,248] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:31:29,265] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:31:29,523] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:33:31,293] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-20 14:33:31,293] INFO _appmap.configuration: config: []
[2024-12-20 14:33:31,293] INFO _appmap.configuration: env: environ({'COMMAND_MODE': 'unix2003', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/miniconda3/envs/codegeex/bin:/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'SHELL': '/bin/zsh', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', '__CFBundleIdentifier': 'com.exafunction.windsurf', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.94.0', 'LANG': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'GIT_ASKPASS': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': '/Applications/Windsurf.app/Contents/Frameworks/Windsurf Helper (Plugin).app/Contents/MacOS/Windsurf Helper (Plugin)', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': '/Applications/Windsurf.app/Contents/Resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/vscode-git-2d46615f3d.sock', 'VSCODE_INJECTION': '1', 'ZDOTDIR': '/Users/<USER>', 'USER_ZDOTDIR': '/Users/<USER>', 'PWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'TERM': 'xterm-256color', 'SHLVL': '1', 'OLDPWD': '/Users/<USER>/Desktop/MCP/codegeex-mcp-test/project_root', 'CONDA_EXE': '/opt/miniconda3/bin/conda', '_CE_M': '', '_CE_CONDA': '', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '1', 'OLLAMA_ORIGINS': '*', 'CONDA_PREFIX': '/opt/miniconda3/envs/codegeex', 'CONDA_DEFAULT_ENV': 'codegeex', 'CONDA_PROMPT_MODIFIER': '(codegeex) ', '_': '/opt/miniconda3/envs/codegeex/bin/python3'})
[2024-12-20 14:33:31,308] INFO _appmap.env: appmap enabled: False
[2024-12-20 14:33:31,546] INFO _appmap.env: appmap enabled: False
[2024-12-22 10:57:04,119] INFO _appmap.env: appmap enabled: False
[2024-12-22 10:57:04,396] INFO _appmap.env: appmap enabled: False
[2024-12-22 11:12:12,566] INFO _appmap.env: appmap enabled: False
[2024-12-22 11:12:12,806] INFO _appmap.env: appmap enabled: False
[2024-12-22 12:40:38,498] INFO _appmap.env: appmap enabled: False
[2024-12-22 12:40:38,736] INFO _appmap.env: appmap enabled: False
[2024-12-22 12:41:45,608] INFO _appmap.env: appmap enabled: False
[2024-12-22 12:41:45,841] INFO _appmap.env: appmap enabled: False
[2024-12-22 12:55:49,676] INFO _appmap.env: appmap enabled: False
[2024-12-23 11:50:32,770] INFO _appmap.env: appmap enabled: False
[2024-12-23 11:50:33,028] INFO _appmap.env: appmap enabled: False
[2024-12-23 12:10:54,358] INFO _appmap.env: appmap enabled: False
[2024-12-23 12:10:54,574] INFO _appmap.env: appmap enabled: False
[2024-12-23 15:45:49,140] INFO _appmap.env: appmap enabled: False
[2024-12-23 15:45:49,409] INFO _appmap.env: appmap enabled: False
[2024-12-23 15:47:55,036] INFO _appmap.env: appmap enabled: False
[2024-12-23 15:47:55,259] INFO _appmap.env: appmap enabled: False
[2024-12-23 15:56:17,884] INFO _appmap.env: appmap enabled: False
[2024-12-23 15:56:18,104] INFO _appmap.env: appmap enabled: False
[2024-12-23 16:03:55,715] INFO _appmap.env: appmap enabled: False
[2024-12-23 16:03:55,949] INFO _appmap.env: appmap enabled: False
[2024-12-23 16:40:36,993] INFO _appmap.env: appmap enabled: False
[2024-12-23 16:40:37,236] INFO _appmap.env: appmap enabled: False
[2024-12-23 16:42:50,384] INFO _appmap.env: appmap enabled: False
[2024-12-23 16:42:50,606] INFO _appmap.env: appmap enabled: False
[2024-12-23 16:46:48,224] INFO _appmap.env: appmap enabled: False
[2024-12-23 16:46:48,441] INFO _appmap.env: appmap enabled: False
[2024-12-23 16:50:52,228] INFO _appmap.env: appmap enabled: False
[2024-12-23 18:06:19,002] INFO _appmap.env: appmap enabled: False
[2024-12-23 18:06:26,146] INFO _appmap.env: appmap enabled: False
[2024-12-23 18:08:20,618] INFO _appmap.env: appmap enabled: False
[2024-12-23 18:08:23,151] INFO _appmap.env: appmap enabled: False
[2024-12-23 18:47:18,301] INFO _appmap.env: appmap enabled: False
[2024-12-23 18:47:18,559] INFO _appmap.env: appmap enabled: False
[2024-12-23 20:55:36,610] INFO _appmap.env: appmap enabled: False
[2024-12-23 20:55:36,854] INFO _appmap.env: appmap enabled: False
[2024-12-23 21:08:09,160] INFO _appmap.env: appmap enabled: False
[2024-12-23 21:08:09,382] INFO _appmap.env: appmap enabled: False
[2024-12-23 21:17:13,419] INFO _appmap.env: appmap enabled: False
[2024-12-23 21:17:13,643] INFO _appmap.env: appmap enabled: False
[2024-12-23 21:17:48,732] INFO _appmap.env: appmap enabled: False
[2024-12-23 21:17:48,915] INFO _appmap.env: appmap enabled: False
[2024-12-23 21:18:31,614] INFO _appmap.env: appmap enabled: False
[2024-12-23 21:18:31,833] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:28:04,851] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:28:05,095] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:29:11,711] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:31:23,441] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:31:23,660] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:33:34,101] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:33:34,319] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:36:54,321] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:36:54,541] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:38:50,160] INFO _appmap.env: appmap enabled: False
[2024-12-24 00:38:50,380] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:23:39,170] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:23:39,465] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:25:14,962] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:25:15,181] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:30:30,627] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:30:30,870] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:31:17,637] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:31:17,816] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:37:24,727] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:37:24,945] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:40:03,438] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:52:48,850] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:52:49,074] INFO _appmap.env: appmap enabled: False
[2024-12-24 14:56:00,650] INFO _appmap.env: appmap enabled: False
[2024-12-24 15:30:29,939] INFO _appmap.env: appmap enabled: False
[2024-12-24 15:30:30,184] INFO _appmap.env: appmap enabled: False
[2024-12-25 11:53:45,140] INFO _appmap.env: appmap enabled: False
[2024-12-25 11:54:02,028] INFO _appmap.env: appmap enabled: False
[2024-12-25 11:54:02,273] INFO _appmap.env: appmap enabled: False
[2024-12-26 11:39:04,410] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-26 11:39:04,411] INFO _appmap.configuration: config: []
[2024-12-26 11:39:04,411] INFO _appmap.configuration: env: environ({'ELECTRON_RUN_AS_NODE': '1', 'COMMAND_MODE': 'unix2003', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '0', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'OLDPWD': '/', 'OLLAMA_ORIGINS': '*', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'PWD': '/', 'SHELL': '/bin/zsh', 'SHLVL': '0', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/42b266171e51a016313f47d0c48aca9295b9cbb2', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'VSCODE_CWD': '/', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/42b266171e51a016313f47d0c48aca9295b9cbb2/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/42b266171e51a016313f47d0c48aca9295b9cbb2","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_PID': '82275', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.29647309.29647316', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', '__CFBundleIdentifier': 'com.microsoft.VSCode', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', 'ELECTRON_NO_ASAR': '1', 'LC_CTYPE': 'UTF-8'})
[2024-12-26 11:39:04,416] INFO _appmap.env: appmap enabled: False
[2024-12-26 11:39:04,879] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-26 11:39:04,879] INFO _appmap.configuration: config: []
[2024-12-26 11:39:04,879] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.29647309.29647316', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/42b266171e51a016313f47d0c48aca9295b9cbb2/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/42b266171e51a016313f47d0c48aca9295b9cbb2","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '82275', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/42b266171e51a016313f47d0c48aca9295b9cbb2', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2024-12-26 11:39:04,881] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-26 11:39:04,882] INFO _appmap.configuration: config: []
[2024-12-26 11:39:04,882] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.29647309.29647316', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/42b266171e51a016313f47d0c48aca9295b9cbb2/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/42b266171e51a016313f47d0c48aca9295b9cbb2","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '82275', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/42b266171e51a016313f47d0c48aca9295b9cbb2', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2024-12-26 11:39:04,888] INFO _appmap.env: appmap enabled: False
[2024-12-26 11:39:04,889] INFO _appmap.env: appmap enabled: False
[2024-12-26 14:43:22,781] INFO _appmap.env: appmap enabled: False
[2024-12-26 14:43:23,110] INFO _appmap.env: appmap enabled: False
[2024-12-26 14:44:07,279] INFO _appmap.env: appmap enabled: False
[2024-12-26 14:44:33,659] INFO _appmap.env: appmap enabled: False
[2024-12-26 14:44:33,878] INFO _appmap.env: appmap enabled: False
[2024-12-26 14:52:46,455] INFO _appmap.env: appmap enabled: False
[2024-12-26 14:52:46,694] INFO _appmap.env: appmap enabled: False
[2024-12-26 15:07:44,821] INFO _appmap.env: appmap enabled: False
[2024-12-26 15:07:59,604] INFO _appmap.env: appmap enabled: False
[2024-12-26 15:07:59,823] INFO _appmap.env: appmap enabled: False
[2024-12-26 15:23:20,258] INFO _appmap.env: appmap enabled: False
[2024-12-26 15:23:20,511] INFO _appmap.env: appmap enabled: False
[2024-12-26 15:25:28,945] INFO _appmap.env: appmap enabled: False
[2024-12-26 15:25:29,175] INFO _appmap.env: appmap enabled: False
[2024-12-26 15:26:07,285] INFO _appmap.env: appmap enabled: False
[2024-12-26 15:26:07,528] INFO _appmap.env: appmap enabled: False
[2024-12-26 15:31:08,748] INFO _appmap.env: appmap enabled: False
[2024-12-26 15:31:08,988] INFO _appmap.env: appmap enabled: False
[2024-12-26 15:58:56,472] INFO _appmap.env: appmap enabled: False
[2024-12-26 15:58:56,725] INFO _appmap.env: appmap enabled: False
[2024-12-26 16:00:23,543] INFO _appmap.env: appmap enabled: False
[2024-12-26 16:00:23,797] INFO _appmap.env: appmap enabled: False
[2024-12-26 16:17:11,230] INFO _appmap.env: appmap enabled: False
[2024-12-26 16:17:11,485] INFO _appmap.env: appmap enabled: False
[2024-12-26 16:20:25,603] INFO _appmap.env: appmap enabled: False
[2024-12-26 16:20:25,856] INFO _appmap.env: appmap enabled: False
[2024-12-26 17:30:49,593] INFO _appmap.env: appmap enabled: False
[2024-12-26 17:32:35,137] INFO _appmap.env: appmap enabled: False
[2024-12-26 17:32:35,391] INFO _appmap.env: appmap enabled: False
[2024-12-26 17:38:01,938] INFO _appmap.env: appmap enabled: False
[2024-12-26 17:38:02,196] INFO _appmap.env: appmap enabled: False
[2024-12-26 18:02:44,767] INFO _appmap.env: appmap enabled: False
[2024-12-26 18:02:45,013] INFO _appmap.env: appmap enabled: False
[2024-12-26 18:06:50,311] INFO _appmap.env: appmap enabled: False
[2024-12-26 18:06:50,566] INFO _appmap.env: appmap enabled: False
[2024-12-26 18:12:07,443] INFO _appmap.env: appmap enabled: False
[2024-12-26 18:14:24,703] INFO _appmap.env: appmap enabled: False
[2024-12-26 18:14:24,937] INFO _appmap.env: appmap enabled: False
[2024-12-26 18:29:16,872] INFO _appmap.env: appmap enabled: False
[2024-12-26 18:29:17,121] INFO _appmap.env: appmap enabled: False
[2024-12-26 18:31:04,068] INFO _appmap.env: appmap enabled: False
[2024-12-26 18:31:04,328] INFO _appmap.env: appmap enabled: False
[2024-12-26 19:57:53,200] INFO _appmap.env: appmap enabled: False
[2024-12-26 19:58:57,999] INFO _appmap.env: appmap enabled: False
[2024-12-26 20:01:41,675] INFO _appmap.env: appmap enabled: False
[2024-12-26 20:04:43,475] INFO _appmap.env: appmap enabled: False
[2024-12-26 20:06:04,600] INFO _appmap.env: appmap enabled: False
[2024-12-26 20:06:04,848] INFO _appmap.env: appmap enabled: False
[2024-12-26 20:23:13,893] INFO _appmap.env: appmap enabled: False
[2024-12-26 20:23:14,155] INFO _appmap.env: appmap enabled: False
[2024-12-26 20:31:23,620] INFO _appmap.env: appmap enabled: False
[2024-12-26 20:31:23,876] INFO _appmap.env: appmap enabled: False
[2024-12-26 20:37:45,621] INFO _appmap.env: appmap enabled: False
[2024-12-26 20:37:45,913] INFO _appmap.env: appmap enabled: False
[2024-12-26 20:48:42,184] INFO _appmap.env: appmap enabled: False
[2024-12-26 20:49:10,965] INFO _appmap.env: appmap enabled: False
[2024-12-26 20:49:11,149] INFO _appmap.env: appmap enabled: False
[2024-12-26 22:24:27,431] INFO _appmap.env: appmap enabled: False
[2024-12-26 22:24:27,684] INFO _appmap.env: appmap enabled: False
[2024-12-26 22:32:23,394] INFO _appmap.env: appmap enabled: False
[2024-12-26 22:33:04,670] INFO _appmap.env: appmap enabled: False
[2024-12-27 00:51:02,268] INFO _appmap.env: appmap enabled: False
[2024-12-27 00:51:02,533] INFO _appmap.env: appmap enabled: False
[2024-12-27 00:52:20,923] INFO _appmap.env: appmap enabled: False
[2024-12-27 00:52:21,106] INFO _appmap.env: appmap enabled: False
[2024-12-27 10:44:35,674] INFO _appmap.env: appmap enabled: False
[2024-12-27 10:44:35,975] INFO _appmap.env: appmap enabled: False
[2024-12-27 10:51:05,232] INFO _appmap.env: appmap enabled: False
[2024-12-27 10:51:12,705] INFO _appmap.env: appmap enabled: False
[2024-12-27 10:53:19,446] INFO _appmap.env: appmap enabled: False
[2024-12-27 10:53:19,709] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:01:36,277] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:01:36,535] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:28:07,313] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:28:07,586] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:29:16,574] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:29:16,815] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:31:02,524] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:31:22,242] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:34:27,668] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:34:27,922] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:36:51,794] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:36:52,050] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:39:42,523] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:39:42,759] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:42:35,628] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:42:35,890] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:43:07,925] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:46:16,957] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:46:17,217] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:57:40,303] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:57:40,562] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:59:13,666] INFO _appmap.env: appmap enabled: False
[2024-12-27 11:59:13,928] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:07:42,576] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:07:42,830] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:19:53,818] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:19:54,071] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:22:18,220] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:22:18,458] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:25:20,782] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:25:36,315] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:25:56,967] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:26:00,101] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:26:00,291] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:27:17,397] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:29:16,907] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:29:17,162] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:29:34,840] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:33:12,166] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:33:12,413] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:39:00,328] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:39:00,584] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:42:21,744] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:42:21,980] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:43:05,208] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:46:06,762] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:46:06,996] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:50:19,616] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:50:19,871] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:55:13,554] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:55:13,807] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:56:43,057] INFO _appmap.env: appmap enabled: False
[2024-12-27 13:56:43,311] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:07:01,896] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:07:02,152] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:13:00,585] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:13:00,808] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:16:28,643] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:16:28,885] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:18:01,419] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:18:01,659] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:19:27,697] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:19:27,954] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:21:47,552] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:21:47,807] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:23:26,113] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:23:26,365] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:30:03,043] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:30:03,299] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:31:21,990] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:31:22,243] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:41:26,418] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:41:26,684] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:42:36,706] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:45:29,825] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:45:30,087] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:57:17,135] INFO _appmap.env: appmap enabled: False
[2024-12-27 14:57:17,409] INFO _appmap.env: appmap enabled: False
[2024-12-27 15:06:07,843] INFO _appmap.env: appmap enabled: False
[2024-12-27 15:06:08,105] INFO _appmap.env: appmap enabled: False
[2024-12-27 15:11:30,906] INFO _appmap.env: appmap enabled: False
[2024-12-27 15:11:31,159] INFO _appmap.env: appmap enabled: False
[2024-12-27 15:12:55,667] INFO _appmap.env: appmap enabled: False
[2024-12-27 15:12:55,942] INFO _appmap.env: appmap enabled: False
[2024-12-27 15:15:33,838] INFO _appmap.env: appmap enabled: False
[2024-12-27 15:15:34,080] INFO _appmap.env: appmap enabled: False
[2024-12-27 15:18:44,024] INFO _appmap.env: appmap enabled: False
[2024-12-27 15:18:44,288] INFO _appmap.env: appmap enabled: False
[2024-12-27 15:22:04,072] INFO _appmap.env: appmap enabled: False
[2024-12-27 15:22:04,322] INFO _appmap.env: appmap enabled: False
[2024-12-27 15:23:17,335] INFO _appmap.env: appmap enabled: False
[2024-12-27 15:23:17,589] INFO _appmap.env: appmap enabled: False
[2024-12-27 15:24:04,232] INFO _appmap.env: appmap enabled: False
[2024-12-27 15:24:04,465] INFO _appmap.env: appmap enabled: False
[2024-12-27 15:25:53,320] INFO _appmap.env: appmap enabled: False
[2024-12-27 15:25:53,564] INFO _appmap.env: appmap enabled: False
[2024-12-27 16:05:59,339] INFO _appmap.env: appmap enabled: False
[2024-12-27 16:13:14,937] INFO _appmap.env: appmap enabled: False
[2024-12-27 16:13:15,183] INFO _appmap.env: appmap enabled: False
[2024-12-27 16:23:16,437] INFO _appmap.env: appmap enabled: False
[2024-12-27 16:23:16,677] INFO _appmap.env: appmap enabled: False
[2024-12-27 16:23:54,017] INFO _appmap.env: appmap enabled: False
[2024-12-27 16:23:54,267] INFO _appmap.env: appmap enabled: False
[2024-12-28 00:05:33,066] INFO _appmap.env: appmap enabled: False
[2024-12-28 00:05:33,294] INFO _appmap.env: appmap enabled: False
[2024-12-28 00:13:13,440] INFO _appmap.env: appmap enabled: False
[2024-12-28 00:13:13,689] INFO _appmap.env: appmap enabled: False
[2024-12-28 00:14:58,108] INFO _appmap.env: appmap enabled: False
[2024-12-28 00:14:58,331] INFO _appmap.env: appmap enabled: False
[2024-12-28 00:20:24,531] INFO _appmap.env: appmap enabled: False
[2024-12-28 00:20:24,764] INFO _appmap.env: appmap enabled: False
[2024-12-28 00:23:32,109] INFO _appmap.env: appmap enabled: False
[2024-12-28 00:23:32,333] INFO _appmap.env: appmap enabled: False
[2024-12-28 00:32:24,496] INFO _appmap.env: appmap enabled: False
[2024-12-28 00:32:24,732] INFO _appmap.env: appmap enabled: False
[2024-12-28 00:34:39,179] INFO _appmap.env: appmap enabled: False
[2024-12-28 00:34:39,399] INFO _appmap.env: appmap enabled: False
[2024-12-28 00:38:29,245] INFO _appmap.env: appmap enabled: False
[2024-12-28 00:38:29,473] INFO _appmap.env: appmap enabled: False
[2024-12-28 00:43:46,854] INFO _appmap.env: appmap enabled: False
[2024-12-28 00:43:47,112] INFO _appmap.env: appmap enabled: False
[2024-12-28 00:44:16,515] INFO _appmap.env: appmap enabled: False
[2024-12-28 00:44:16,748] INFO _appmap.env: appmap enabled: False
[2024-12-30 10:44:04,700] INFO _appmap.env: appmap enabled: False
[2024-12-30 10:44:05,077] INFO _appmap.env: appmap enabled: False
[2024-12-30 10:48:32,912] INFO _appmap.env: appmap enabled: False
[2024-12-30 10:48:33,145] INFO _appmap.env: appmap enabled: False
[2024-12-30 10:56:26,438] INFO _appmap.env: appmap enabled: False
[2024-12-30 10:56:26,680] INFO _appmap.env: appmap enabled: False
[2024-12-30 10:57:36,520] INFO _appmap.env: appmap enabled: False
[2024-12-30 10:57:36,704] INFO _appmap.env: appmap enabled: False
[2024-12-30 11:15:18,988] INFO _appmap.env: appmap enabled: False
[2024-12-30 11:15:19,237] INFO _appmap.env: appmap enabled: False
[2024-12-30 11:24:18,669] INFO _appmap.env: appmap enabled: False
[2024-12-30 11:24:18,933] INFO _appmap.env: appmap enabled: False
[2024-12-30 11:25:03,054] INFO _appmap.env: appmap enabled: False
[2024-12-30 11:25:03,282] INFO _appmap.env: appmap enabled: False
[2024-12-30 11:50:02,977] INFO _appmap.env: appmap enabled: False
[2024-12-30 11:50:03,314] INFO _appmap.env: appmap enabled: False
[2024-12-30 11:51:50,783] INFO _appmap.env: appmap enabled: False
[2024-12-30 11:51:51,006] INFO _appmap.env: appmap enabled: False
[2024-12-30 11:52:42,369] INFO _appmap.env: appmap enabled: False
[2024-12-30 11:53:01,986] INFO _appmap.env: appmap enabled: False
[2024-12-30 11:53:36,903] INFO _appmap.env: appmap enabled: False
[2024-12-30 11:53:37,087] INFO _appmap.env: appmap enabled: False
[2024-12-30 11:54:14,131] INFO _appmap.env: appmap enabled: False
[2024-12-30 14:02:35,130] INFO _appmap.env: appmap enabled: False
[2024-12-30 14:02:35,405] INFO _appmap.env: appmap enabled: False
[2024-12-30 14:06:11,000] INFO _appmap.env: appmap enabled: False
[2024-12-30 14:06:11,226] INFO _appmap.env: appmap enabled: False
[2024-12-30 14:09:08,696] INFO _appmap.env: appmap enabled: False
[2024-12-30 14:09:08,920] INFO _appmap.env: appmap enabled: False
[2024-12-30 14:24:56,210] INFO _appmap.env: appmap enabled: False
[2024-12-30 14:24:56,451] INFO _appmap.env: appmap enabled: False
[2024-12-30 14:25:30,482] INFO _appmap.env: appmap enabled: False
[2024-12-30 14:29:39,571] INFO _appmap.env: appmap enabled: False
[2024-12-30 14:29:39,814] INFO _appmap.env: appmap enabled: False
[2024-12-30 14:34:27,335] INFO _appmap.env: appmap enabled: False
[2024-12-30 14:34:27,587] INFO _appmap.env: appmap enabled: False
[2024-12-30 14:38:24,159] INFO _appmap.env: appmap enabled: False
[2024-12-30 14:38:24,397] INFO _appmap.env: appmap enabled: False
[2024-12-30 14:39:03,623] INFO _appmap.env: appmap enabled: False
[2024-12-30 15:02:42,559] INFO _appmap.env: appmap enabled: False
[2024-12-30 15:02:42,839] INFO _appmap.env: appmap enabled: False
[2024-12-30 15:19:24,696] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 15:19:24,698] INFO _appmap.configuration: config: []
[2024-12-30 15:19:24,699] INFO _appmap.configuration: env: environ({'ELECTRON_RUN_AS_NODE': '1', 'COMMAND_MODE': 'unix2003', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '0', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'OLDPWD': '/', 'OLLAMA_ORIGINS': '*', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'PWD': '/', 'SHELL': '/bin/zsh', 'SHLVL': '0', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'VSCODE_CWD': '/', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_PID': '99343', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', '__CFBundleIdentifier': 'com.microsoft.VSCode', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', 'ELECTRON_NO_ASAR': '1', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 15:19:24,704] INFO _appmap.env: appmap enabled: False
[2024-12-30 15:19:24,953] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 15:19:24,954] INFO _appmap.configuration: config: []
[2024-12-30 15:19:24,957] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 15:19:24,957] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 15:19:24,958] INFO _appmap.configuration: config: []
[2024-12-30 15:19:24,958] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 15:19:24,965] INFO _appmap.env: appmap enabled: False
[2024-12-30 15:19:24,967] INFO _appmap.env: appmap enabled: False
[2024-12-30 15:42:15,722] INFO _appmap.env: appmap enabled: False
[2024-12-30 15:42:15,980] INFO _appmap.env: appmap enabled: False
[2024-12-30 16:42:00,155] INFO _appmap.env: appmap enabled: False
[2024-12-30 16:42:00,509] INFO _appmap.env: appmap enabled: False
[2024-12-30 17:21:37,682] INFO _appmap.env: appmap enabled: False
[2024-12-30 17:21:37,941] INFO _appmap.env: appmap enabled: False
[2024-12-30 17:37:49,926] INFO _appmap.env: appmap enabled: False
[2024-12-30 17:37:50,194] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:26:48,270] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:26:48,558] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:27:39,074] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:31:49,893] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:31:49,894] INFO _appmap.configuration: config: []
[2024-12-30 18:31:49,894] INFO _appmap.configuration: env: environ({'ELECTRON_RUN_AS_NODE': '1', 'COMMAND_MODE': 'unix2003', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '0', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'OLDPWD': '/', 'OLLAMA_ORIGINS': '*', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'PWD': '/', 'SHELL': '/bin/zsh', 'SHLVL': '0', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'VSCODE_CWD': '/', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_PID': '99343', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', '__CFBundleIdentifier': 'com.microsoft.VSCode', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'ELECTRON_NO_ASAR': '1', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:31:49,900] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:31:50,179] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:31:50,179] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:31:50,179] INFO _appmap.configuration: config: []
[2024-12-30 18:31:50,179] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:31:50,179] INFO _appmap.configuration: config: []
[2024-12-30 18:31:50,179] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:31:50,184] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:31:50,184] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:31:59,743] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:31:59,744] INFO _appmap.configuration: config: []
[2024-12-30 18:31:59,744] INFO _appmap.configuration: env: environ({'ELECTRON_RUN_AS_NODE': '1', 'COMMAND_MODE': 'unix2003', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '0', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'OLDPWD': '/', 'OLLAMA_ORIGINS': '*', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'PWD': '/', 'SHELL': '/bin/zsh', 'SHLVL': '0', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'VSCODE_CWD': '/', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_PID': '99343', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', '__CFBundleIdentifier': 'com.microsoft.VSCode', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'ELECTRON_NO_ASAR': '1', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:31:59,750] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:32:00,005] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:32:00,005] INFO _appmap.configuration: config: []
[2024-12-30 18:32:00,005] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:32:00,005] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:32:00,006] INFO _appmap.configuration: config: []
[2024-12-30 18:32:00,006] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:32:00,011] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:32:00,012] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:33:29,999] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:33:29,999] INFO _appmap.configuration: config: []
[2024-12-30 18:33:29,999] INFO _appmap.configuration: env: environ({'ELECTRON_RUN_AS_NODE': '1', 'COMMAND_MODE': 'unix2003', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '0', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'OLDPWD': '/', 'OLLAMA_ORIGINS': '*', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'PWD': '/', 'SHELL': '/bin/zsh', 'SHLVL': '0', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'VSCODE_CWD': '/', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_PID': '99343', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', '__CFBundleIdentifier': 'com.microsoft.VSCode', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'ELECTRON_NO_ASAR': '1', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:33:30,005] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:33:30,279] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:33:30,280] INFO _appmap.configuration: config: []
[2024-12-30 18:33:30,280] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:33:30,281] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:33:30,281] INFO _appmap.configuration: config: []
[2024-12-30 18:33:30,281] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:33:30,287] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:33:30,288] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:34:08,764] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:34:08,764] INFO _appmap.configuration: config: []
[2024-12-30 18:34:08,764] INFO _appmap.configuration: env: environ({'ELECTRON_RUN_AS_NODE': '1', 'COMMAND_MODE': 'unix2003', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '0', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'OLDPWD': '/', 'OLLAMA_ORIGINS': '*', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'PWD': '/', 'SHELL': '/bin/zsh', 'SHLVL': '0', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'VSCODE_CWD': '/', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_PID': '99343', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', '__CFBundleIdentifier': 'com.microsoft.VSCode', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'ELECTRON_NO_ASAR': '1', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:34:08,770] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:34:09,093] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:34:09,094] INFO _appmap.configuration: config: []
[2024-12-30 18:34:09,094] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:34:09,094] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:34:09,094] INFO _appmap.configuration: config: []
[2024-12-30 18:34:09,094] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:34:09,100] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:34:09,100] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:34:53,160] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:34:53,161] INFO _appmap.configuration: config: []
[2024-12-30 18:34:53,161] INFO _appmap.configuration: env: environ({'ELECTRON_RUN_AS_NODE': '1', 'COMMAND_MODE': 'unix2003', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '0', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'OLDPWD': '/', 'OLLAMA_ORIGINS': '*', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'PWD': '/', 'SHELL': '/bin/zsh', 'SHLVL': '0', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'VSCODE_CWD': '/', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_PID': '99343', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', '__CFBundleIdentifier': 'com.microsoft.VSCode', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'ELECTRON_NO_ASAR': '1', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:34:53,168] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:34:53,515] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:34:53,515] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:34:53,515] INFO _appmap.configuration: config: []
[2024-12-30 18:34:53,515] INFO _appmap.configuration: config: []
[2024-12-30 18:34:53,515] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:34:53,515] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:34:53,524] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:34:53,524] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:35:03,496] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:35:03,496] INFO _appmap.configuration: config: []
[2024-12-30 18:35:03,496] INFO _appmap.configuration: env: environ({'ELECTRON_RUN_AS_NODE': '1', 'COMMAND_MODE': 'unix2003', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '0', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'OLDPWD': '/', 'OLLAMA_ORIGINS': '*', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'PWD': '/', 'SHELL': '/bin/zsh', 'SHLVL': '0', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'VSCODE_CWD': '/', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_PID': '99343', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', '__CFBundleIdentifier': 'com.microsoft.VSCode', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'ELECTRON_NO_ASAR': '1', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:35:03,502] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:35:03,747] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:35:03,747] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:35:03,747] INFO _appmap.configuration: config: []
[2024-12-30 18:35:03,747] INFO _appmap.configuration: config: []
[2024-12-30 18:35:03,747] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:35:03,747] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:35:03,753] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:35:03,753] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:35:46,066] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:35:46,066] INFO _appmap.configuration: config: []
[2024-12-30 18:35:46,066] INFO _appmap.configuration: env: environ({'ELECTRON_RUN_AS_NODE': '1', 'COMMAND_MODE': 'unix2003', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '0', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'OLDPWD': '/', 'OLLAMA_ORIGINS': '*', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'PWD': '/', 'SHELL': '/bin/zsh', 'SHLVL': '0', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'VSCODE_CWD': '/', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_PID': '99343', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', '__CFBundleIdentifier': 'com.microsoft.VSCode', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'ELECTRON_NO_ASAR': '1', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:35:46,072] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:35:46,359] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:35:46,360] INFO _appmap.configuration: config: []
[2024-12-30 18:35:46,360] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:35:46,361] INFO _appmap.configuration: file: [no appmap.yml]
[2024-12-30 18:35:46,361] INFO _appmap.configuration: config: []
[2024-12-30 18:35:46,361] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2024-12-30 18:35:46,366] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:35:46,366] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:53:48,852] INFO _appmap.env: appmap enabled: False
[2024-12-30 18:53:49,124] INFO _appmap.env: appmap enabled: False
[2024-12-30 19:11:57,775] INFO _appmap.env: appmap enabled: False
[2024-12-30 19:11:58,028] INFO _appmap.env: appmap enabled: False
[2024-12-30 19:26:37,504] INFO _appmap.env: appmap enabled: False
[2024-12-30 19:26:37,779] INFO _appmap.env: appmap enabled: False
[2024-12-30 20:24:53,790] INFO _appmap.env: appmap enabled: False
[2024-12-30 20:24:54,049] INFO _appmap.env: appmap enabled: False
[2024-12-30 20:29:18,367] INFO _appmap.env: appmap enabled: False
[2024-12-30 20:29:18,588] INFO _appmap.env: appmap enabled: False
[2024-12-30 20:30:16,030] INFO _appmap.env: appmap enabled: False
[2024-12-30 20:30:16,251] INFO _appmap.env: appmap enabled: False
[2024-12-30 20:32:02,476] INFO _appmap.env: appmap enabled: False
[2024-12-30 20:32:02,700] INFO _appmap.env: appmap enabled: False
[2024-12-30 20:42:05,125] INFO _appmap.env: appmap enabled: False
[2024-12-30 20:42:05,371] INFO _appmap.env: appmap enabled: False
[2024-12-31 10:08:41,373] INFO _appmap.env: appmap enabled: False
[2024-12-31 10:08:41,630] INFO _appmap.env: appmap enabled: False
[2024-12-31 10:29:14,260] INFO _appmap.env: appmap enabled: False
[2024-12-31 10:29:14,501] INFO _appmap.env: appmap enabled: False
[2024-12-31 10:35:42,850] INFO _appmap.env: appmap enabled: False
[2024-12-31 10:35:43,094] INFO _appmap.env: appmap enabled: False
[2024-12-31 10:52:06,723] INFO _appmap.env: appmap enabled: False
[2024-12-31 10:52:06,967] INFO _appmap.env: appmap enabled: False
[2024-12-31 10:56:29,791] INFO _appmap.env: appmap enabled: False
[2024-12-31 10:56:30,030] INFO _appmap.env: appmap enabled: False
[2025-01-06 21:45:10,490] INFO _appmap.env: appmap enabled: False
[2025-01-06 21:45:24,990] INFO _appmap.env: appmap enabled: False
[2025-01-06 21:45:25,172] INFO _appmap.env: appmap enabled: False
[2025-01-06 21:55:47,722] INFO _appmap.env: appmap enabled: False
[2025-01-06 21:55:47,971] INFO _appmap.env: appmap enabled: False
[2025-01-06 21:56:38,406] INFO _appmap.env: appmap enabled: False
[2025-01-06 22:34:03,851] INFO _appmap.env: appmap enabled: False
[2025-01-06 22:34:04,118] INFO _appmap.env: appmap enabled: False
[2025-01-06 22:36:02,174] INFO _appmap.env: appmap enabled: False
[2025-01-06 22:36:15,724] INFO _appmap.env: appmap enabled: False
[2025-01-06 22:37:39,917] INFO _appmap.env: appmap enabled: False
[2025-01-06 22:37:40,155] INFO _appmap.env: appmap enabled: False
[2025-01-06 22:38:17,158] INFO _appmap.env: appmap enabled: False
[2025-01-06 22:41:00,461] INFO _appmap.env: appmap enabled: False
[2025-01-06 22:41:44,878] INFO _appmap.env: appmap enabled: False
[2025-01-06 22:41:45,129] INFO _appmap.env: appmap enabled: False
[2025-01-06 22:42:45,627] INFO _appmap.env: appmap enabled: False
[2025-01-06 22:48:12,738] INFO _appmap.env: appmap enabled: False
[2025-01-06 22:48:25,278] INFO _appmap.env: appmap enabled: False
[2025-01-06 22:48:37,822] INFO _appmap.env: appmap enabled: False
[2025-01-06 22:49:03,819] INFO _appmap.env: appmap enabled: False
[2025-01-06 22:49:04,087] INFO _appmap.env: appmap enabled: False
[2025-01-06 22:58:58,408] INFO _appmap.env: appmap enabled: False
[2025-01-06 22:58:58,704] INFO _appmap.env: appmap enabled: False
[2025-01-06 23:49:52,584] INFO _appmap.env: appmap enabled: False
[2025-01-06 23:49:52,940] INFO _appmap.env: appmap enabled: False
[2025-01-06 23:57:24,544] INFO _appmap.env: appmap enabled: False
[2025-01-06 23:57:24,780] INFO _appmap.env: appmap enabled: False
[2025-01-06 23:59:51,398] INFO _appmap.env: appmap enabled: False
[2025-01-06 23:59:51,635] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:27:54,954] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:27:55,243] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:30:53,369] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:30:53,608] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:32:38,365] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:32:38,589] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:36:45,074] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:36:45,310] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:37:28,532] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:39:34,447] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:39:34,697] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:45:46,290] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:45:46,543] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:47:39,219] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:47:54,861] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:50:21,548] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:50:21,800] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:56:13,884] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:56:14,122] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:57:48,479] INFO _appmap.env: appmap enabled: False
[2025-01-07 00:58:02,083] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:00:49,172] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:11:49,013] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:11:49,296] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:15:13,098] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:16:05,033] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:16:05,258] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:16:46,501] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:23:24,908] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:23:25,149] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:30:04,611] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:30:04,865] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:36:38,934] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:36:39,170] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:39:56,224] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:39:56,461] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:40:57,976] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:40:58,230] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:44:08,401] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:44:08,653] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:46:44,876] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:46:45,130] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:49:03,745] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:49:03,986] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:56:18,357] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:56:18,638] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:57:59,118] INFO _appmap.env: appmap enabled: False
[2025-01-07 01:57:59,368] INFO _appmap.env: appmap enabled: False
[2025-01-07 10:31:10,696] INFO _appmap.env: appmap enabled: False
[2025-01-07 10:31:11,031] INFO _appmap.env: appmap enabled: False
[2025-01-07 10:32:49,452] INFO _appmap.env: appmap enabled: False
[2025-01-07 10:32:53,914] INFO _appmap.env: appmap enabled: False
[2025-01-07 10:32:54,106] INFO _appmap.env: appmap enabled: False
[2025-01-07 10:47:00,391] INFO _appmap.env: appmap enabled: False
[2025-01-07 10:47:29,422] INFO _appmap.env: appmap enabled: False
[2025-01-07 10:47:29,662] INFO _appmap.env: appmap enabled: False
[2025-01-07 10:54:08,689] INFO _appmap.env: appmap enabled: False
[2025-01-07 10:54:08,949] INFO _appmap.env: appmap enabled: False
[2025-01-07 10:59:11,767] INFO _appmap.env: appmap enabled: False
[2025-01-07 10:59:12,019] INFO _appmap.env: appmap enabled: False
[2025-01-07 11:01:31,411] INFO _appmap.env: appmap enabled: False
[2025-01-07 11:01:31,671] INFO _appmap.env: appmap enabled: False
[2025-01-07 11:02:54,458] INFO _appmap.env: appmap enabled: False
[2025-01-07 11:02:54,704] INFO _appmap.env: appmap enabled: False
[2025-01-07 11:05:58,974] INFO _appmap.env: appmap enabled: False
[2025-01-07 11:05:59,233] INFO _appmap.env: appmap enabled: False
[2025-01-07 11:14:08,048] INFO _appmap.env: appmap enabled: False
[2025-01-07 11:14:08,406] INFO _appmap.env: appmap enabled: False
[2025-01-07 11:23:29,654] INFO _appmap.env: appmap enabled: False
[2025-01-07 11:23:29,893] INFO _appmap.env: appmap enabled: False
[2025-01-07 11:26:31,497] INFO _appmap.env: appmap enabled: False
[2025-01-07 11:49:46,473] INFO _appmap.env: appmap enabled: False
[2025-01-07 11:49:46,732] INFO _appmap.env: appmap enabled: False
[2025-01-07 14:14:45,989] INFO _appmap.env: appmap enabled: False
[2025-01-07 14:14:46,325] INFO _appmap.env: appmap enabled: False
[2025-01-07 17:27:52,556] INFO _appmap.env: appmap enabled: False
[2025-01-07 17:27:52,807] INFO _appmap.env: appmap enabled: False
[2025-01-07 17:43:44,643] INFO _appmap.env: appmap enabled: False
[2025-01-07 17:43:44,902] INFO _appmap.env: appmap enabled: False
[2025-01-07 17:52:57,179] INFO _appmap.env: appmap enabled: False
[2025-01-07 17:52:57,470] INFO _appmap.env: appmap enabled: False
[2025-01-07 17:58:33,737] INFO _appmap.env: appmap enabled: False
[2025-01-07 17:58:33,994] INFO _appmap.env: appmap enabled: False
[2025-01-07 17:58:52,640] INFO _appmap.env: appmap enabled: False
[2025-01-07 17:59:26,385] INFO _appmap.env: appmap enabled: False
[2025-01-07 17:59:26,636] INFO _appmap.env: appmap enabled: False
[2025-01-07 17:59:37,171] INFO _appmap.env: appmap enabled: False
[2025-01-07 18:01:11,641] INFO _appmap.env: appmap enabled: False
[2025-01-07 18:01:11,884] INFO _appmap.env: appmap enabled: False
[2025-01-07 18:01:37,813] INFO _appmap.env: appmap enabled: False
[2025-01-07 18:02:24,380] INFO _appmap.env: appmap enabled: False
[2025-01-07 18:02:24,601] INFO _appmap.env: appmap enabled: False
[2025-01-07 18:02:49,466] INFO _appmap.env: appmap enabled: False
[2025-01-07 18:03:05,146] INFO _appmap.env: appmap enabled: False
[2025-01-07 18:04:19,243] INFO _appmap.env: appmap enabled: False
[2025-01-07 18:54:40,663] INFO _appmap.env: appmap enabled: False
[2025-01-07 18:55:09,527] INFO _appmap.env: appmap enabled: False
[2025-01-07 18:55:09,769] INFO _appmap.env: appmap enabled: False
[2025-01-07 18:57:13,071] INFO _appmap.env: appmap enabled: False
[2025-01-07 18:57:13,316] INFO _appmap.env: appmap enabled: False
[2025-01-07 20:04:02,576] INFO _appmap.env: appmap enabled: False
[2025-01-07 20:04:02,840] INFO _appmap.env: appmap enabled: False
[2025-01-07 20:05:53,424] INFO _appmap.env: appmap enabled: False
[2025-01-07 20:05:53,664] INFO _appmap.env: appmap enabled: False
[2025-01-07 20:07:17,587] INFO _appmap.env: appmap enabled: False
[2025-01-07 20:07:43,261] INFO _appmap.env: appmap enabled: False
[2025-01-07 20:07:43,486] INFO _appmap.env: appmap enabled: False
[2025-01-07 20:13:46,214] INFO _appmap.env: appmap enabled: False
[2025-01-07 20:13:46,451] INFO _appmap.env: appmap enabled: False
[2025-01-07 20:15:06,635] INFO _appmap.env: appmap enabled: False
[2025-01-07 20:15:16,174] INFO _appmap.env: appmap enabled: False
[2025-01-07 20:17:23,469] INFO _appmap.env: appmap enabled: False
[2025-01-07 20:19:04,672] INFO _appmap.env: appmap enabled: False
[2025-01-07 20:19:04,899] INFO _appmap.env: appmap enabled: False
[2025-01-07 21:35:57,683] INFO _appmap.env: appmap enabled: False
[2025-01-07 21:35:57,967] INFO _appmap.env: appmap enabled: False
[2025-01-07 21:36:56,542] INFO _appmap.env: appmap enabled: False
[2025-01-07 21:38:43,703] INFO _appmap.env: appmap enabled: False
[2025-01-07 21:38:43,943] INFO _appmap.env: appmap enabled: False
[2025-01-08 00:00:08,148] INFO _appmap.env: appmap enabled: False
[2025-01-08 00:00:08,417] INFO _appmap.env: appmap enabled: False
[2025-01-08 00:01:15,949] INFO _appmap.env: appmap enabled: False
[2025-01-08 00:08:24,304] INFO _appmap.env: appmap enabled: False
[2025-01-08 00:09:47,535] INFO _appmap.env: appmap enabled: False
[2025-01-08 00:10:30,401] INFO _appmap.env: appmap enabled: False
[2025-01-08 00:10:30,716] INFO _appmap.env: appmap enabled: False
[2025-01-08 00:10:30,716] INFO _appmap.env: appmap enabled: False
[2025-01-08 00:11:38,363] INFO _appmap.env: appmap enabled: False
[2025-01-08 00:11:54,627] INFO _appmap.env: appmap enabled: False
[2025-01-08 00:13:53,533] INFO _appmap.env: appmap enabled: False
[2025-01-08 00:15:13,233] INFO _appmap.env: appmap enabled: False
[2025-01-08 00:18:20,662] INFO _appmap.env: appmap enabled: False
[2025-01-08 00:27:48,308] INFO _appmap.env: appmap enabled: False
[2025-01-08 01:08:40,762] INFO _appmap.env: appmap enabled: False
[2025-01-08 01:12:41,733] INFO _appmap.env: appmap enabled: False
[2025-01-08 01:12:49,233] INFO _appmap.env: appmap enabled: False
[2025-01-08 01:16:23,819] INFO _appmap.env: appmap enabled: False
[2025-01-08 01:16:24,178] INFO _appmap.env: appmap enabled: False
[2025-01-08 01:16:24,178] INFO _appmap.env: appmap enabled: False
[2025-01-08 01:17:29,287] INFO _appmap.env: appmap enabled: False
[2025-01-08 01:17:29,585] INFO _appmap.env: appmap enabled: False
[2025-01-08 01:17:29,585] INFO _appmap.env: appmap enabled: False
[2025-01-08 01:18:27,496] INFO _appmap.env: appmap enabled: False
[2025-01-08 01:18:27,776] INFO _appmap.env: appmap enabled: False
[2025-01-08 01:18:27,776] INFO _appmap.env: appmap enabled: False
[2025-01-08 01:24:39,903] INFO _appmap.env: appmap enabled: False
[2025-01-08 01:24:40,204] INFO _appmap.env: appmap enabled: False
[2025-01-08 01:24:40,204] INFO _appmap.env: appmap enabled: False
[2025-01-08 10:22:40,959] INFO _appmap.env: appmap enabled: False
[2025-01-08 10:22:41,286] INFO _appmap.env: appmap enabled: False
[2025-01-08 10:22:41,286] INFO _appmap.env: appmap enabled: False
[2025-01-08 16:32:41,041] INFO _appmap.env: appmap enabled: False
[2025-01-08 16:32:41,438] INFO _appmap.env: appmap enabled: False
[2025-01-08 16:32:41,438] INFO _appmap.env: appmap enabled: False
[2025-01-08 16:33:10,836] INFO _appmap.env: appmap enabled: False
[2025-01-08 16:33:11,112] INFO _appmap.env: appmap enabled: False
[2025-01-08 16:33:11,112] INFO _appmap.env: appmap enabled: False
[2025-01-08 16:35:36,338] INFO _appmap.env: appmap enabled: False
[2025-01-09 14:11:38,904] INFO _appmap.configuration: file: [no appmap.yml]
[2025-01-09 14:11:38,906] INFO _appmap.configuration: config: []
[2025-01-09 14:11:38,907] INFO _appmap.configuration: env: environ({'ELECTRON_RUN_AS_NODE': '1', 'COMMAND_MODE': 'unix2003', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '0', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'OLDPWD': '/', 'OLLAMA_ORIGINS': '*', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'PWD': '/', 'SHELL': '/bin/zsh', 'SHLVL': '0', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'VSCODE_CWD': '/', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_PID': '99343', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', '__CFBundleIdentifier': 'com.microsoft.VSCode', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'ELECTRON_NO_ASAR': '1', 'LC_CTYPE': 'UTF-8'})
[2025-01-09 14:11:38,929] INFO _appmap.env: appmap enabled: False
[2025-01-09 14:11:39,260] INFO _appmap.configuration: file: [no appmap.yml]
[2025-01-09 14:11:39,260] INFO _appmap.configuration: config: []
[2025-01-09 14:11:39,260] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2025-01-09 14:11:39,260] INFO _appmap.configuration: file: [no appmap.yml]
[2025-01-09 14:11:39,260] INFO _appmap.configuration: config: []
[2025-01-09 14:11:39,260] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2025-01-09 14:11:39,269] INFO _appmap.env: appmap enabled: False
[2025-01-09 14:11:39,269] INFO _appmap.env: appmap enabled: False
[2025-01-09 14:12:05,829] INFO _appmap.configuration: file: [no appmap.yml]
[2025-01-09 14:12:05,830] INFO _appmap.configuration: config: []
[2025-01-09 14:12:05,830] INFO _appmap.configuration: env: environ({'ELECTRON_RUN_AS_NODE': '1', 'COMMAND_MODE': 'unix2003', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '0', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'OLDPWD': '/', 'OLLAMA_ORIGINS': '*', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'PWD': '/', 'SHELL': '/bin/zsh', 'SHLVL': '0', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'VSCODE_CWD': '/', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_PID': '99343', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', '__CFBundleIdentifier': 'com.microsoft.VSCode', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'ELECTRON_NO_ASAR': '1', 'LC_CTYPE': 'UTF-8'})
[2025-01-09 14:12:05,836] INFO _appmap.env: appmap enabled: False
[2025-01-09 14:12:06,139] INFO _appmap.configuration: file: [no appmap.yml]
[2025-01-09 14:12:06,139] INFO _appmap.configuration: config: []
[2025-01-09 14:12:06,139] INFO _appmap.configuration: file: [no appmap.yml]
[2025-01-09 14:12:06,139] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2025-01-09 14:12:06,139] INFO _appmap.configuration: config: []
[2025-01-09 14:12:06,139] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2025-01-09 14:12:06,147] INFO _appmap.env: appmap enabled: False
[2025-01-09 14:12:06,147] INFO _appmap.env: appmap enabled: False
[2025-01-09 14:12:24,878] INFO _appmap.configuration: file: [no appmap.yml]
[2025-01-09 14:12:24,879] INFO _appmap.configuration: config: []
[2025-01-09 14:12:24,879] INFO _appmap.configuration: env: environ({'ELECTRON_RUN_AS_NODE': '1', 'COMMAND_MODE': 'unix2003', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '0', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'OLDPWD': '/', 'OLLAMA_ORIGINS': '*', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'PWD': '/', 'SHELL': '/bin/zsh', 'SHLVL': '0', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'VSCODE_CWD': '/', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_PID': '99343', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', '__CFBundleIdentifier': 'com.microsoft.VSCode', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'ELECTRON_NO_ASAR': '1', 'LC_CTYPE': 'UTF-8'})
[2025-01-09 14:12:24,884] INFO _appmap.env: appmap enabled: False
[2025-01-09 14:12:25,261] INFO _appmap.configuration: file: [no appmap.yml]
[2025-01-09 14:12:25,261] INFO _appmap.configuration: config: []
[2025-01-09 14:12:25,261] INFO _appmap.configuration: file: [no appmap.yml]
[2025-01-09 14:12:25,262] INFO _appmap.configuration: config: []
[2025-01-09 14:12:25,262] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2025-01-09 14:12:25,262] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2025-01-09 14:12:25,268] INFO _appmap.env: appmap enabled: False
[2025-01-09 14:12:25,268] INFO _appmap.env: appmap enabled: False
[2025-01-09 14:12:49,847] INFO _appmap.configuration: file: [no appmap.yml]
[2025-01-09 14:12:49,849] INFO _appmap.configuration: config: []
[2025-01-09 14:12:49,849] INFO _appmap.configuration: env: environ({'ELECTRON_RUN_AS_NODE': '1', 'COMMAND_MODE': 'unix2003', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '0', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'OLDPWD': '/', 'OLLAMA_ORIGINS': '*', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'PWD': '/', 'SHELL': '/bin/zsh', 'SHLVL': '0', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'VSCODE_CWD': '/', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_PID': '99343', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', '__CFBundleIdentifier': 'com.microsoft.VSCode', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'ELECTRON_NO_ASAR': '1', 'LC_CTYPE': 'UTF-8'})
[2025-01-09 14:12:49,857] INFO _appmap.env: appmap enabled: False
[2025-01-09 14:12:50,148] INFO _appmap.configuration: file: [no appmap.yml]
[2025-01-09 14:12:50,148] INFO _appmap.configuration: config: []
[2025-01-09 14:12:50,149] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2025-01-09 14:12:50,149] INFO _appmap.configuration: file: [no appmap.yml]
[2025-01-09 14:12:50,149] INFO _appmap.configuration: config: []
[2025-01-09 14:12:50,149] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2025-01-09 14:12:50,155] INFO _appmap.env: appmap enabled: False
[2025-01-09 14:12:50,156] INFO _appmap.env: appmap enabled: False
[2025-01-09 14:13:00,051] INFO _appmap.configuration: file: [no appmap.yml]
[2025-01-09 14:13:00,051] INFO _appmap.configuration: config: []
[2025-01-09 14:13:00,052] INFO _appmap.configuration: env: environ({'ELECTRON_RUN_AS_NODE': '1', 'COMMAND_MODE': 'unix2003', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'CONDA_SHLVL': '0', 'HOME': '/Users/<USER>', 'LOGNAME': 'liurongxuan', 'MallocNanoZone': '0', 'OLDPWD': '/', 'OLLAMA_ORIGINS': '*', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'PWD': '/', 'SHELL': '/bin/zsh', 'SHLVL': '0', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'USER': 'liurongxuan', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'VSCODE_CWD': '/', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_PID': '99343', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', '__CFBundleIdentifier': 'com.microsoft.VSCode', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'ELECTRON_NO_ASAR': '1', 'LC_CTYPE': 'UTF-8'})
[2025-01-09 14:13:00,059] INFO _appmap.env: appmap enabled: False
[2025-01-09 14:13:00,341] INFO _appmap.configuration: file: [no appmap.yml]
[2025-01-09 14:13:00,341] INFO _appmap.configuration: config: []
[2025-01-09 14:13:00,341] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2025-01-09 14:13:00,341] INFO _appmap.configuration: file: [no appmap.yml]
[2025-01-09 14:13:00,341] INFO _appmap.configuration: config: []
[2025-01-09 14:13:00,341] INFO _appmap.configuration: env: environ({'ELECTRON_NO_ASAR': '1', 'APPLICATIONINSIGHTS_CONFIGURATION_CONTENT': '{}', 'APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL': '1', 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.96.2024121109/translations/extensions/vscode.json-language-features.i18n.json', '__CFBundleIdentifier': 'com.microsoft.VSCode', 'XPC_SERVICE_NAME': 'application.com.microsoft.VSCode.30265238.30265244', 'XPC_FLAGS': '0x0', 'VSCODE_NLS_CONFIG': '{"userLocale":"zh-cn","osLocale":"en-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/fabdb6a30b49f79a7aba0f2ad9df9b399473380f","_corruptedFile":"/Users/<USER>/Library/Application Support/Code/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}', 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.96-main.sock', '_': '/Applications/Visual Studio Code.app/Contents/MacOS/Electron', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'COMMAND_MODE': 'unix2003', 'VSCODE_CWD': '/', 'VSCODE_PID': '99343', 'CONDA_SHLVL': '0', 'SHLVL': '0', 'TMPDIR': '/var/folders/3d/3mphs9257kd06lhc2drdvdww0000gn/T/', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.crewMabS4t/Listeners', 'SHELL': '/bin/zsh', 'PATH': '/opt/homebrew/bin:/opt/miniconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin', 'OLLAMA_ORIGINS': '*', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/fabdb6a30b49f79a7aba0f2ad9df9b399473380f', 'LOGNAME': 'liurongxuan', 'CONDA_EXE': '/opt/miniconda3/bin/conda', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OLDPWD': '/', 'USER': 'liurongxuan', 'MallocNanoZone': '0', 'HOME': '/Users/<USER>', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'PWD': '/', 'CONDA_PYTHON_EXE': '/opt/miniconda3/bin/python', 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost', 'LC_CTYPE': 'UTF-8'})
[2025-01-09 14:13:00,348] INFO _appmap.env: appmap enabled: False
[2025-01-09 14:13:00,349] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:09:20,166] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:09:20,529] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:09:20,529] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:12:41,613] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:12:41,891] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:12:41,891] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:14:57,256] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:14:57,550] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:14:57,550] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:16:56,167] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:16:56,463] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:16:56,463] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:20:30,658] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:20:30,938] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:20:30,938] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:23:13,195] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:23:13,471] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:23:13,471] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:24:36,297] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:24:36,566] INFO _appmap.env: appmap enabled: False
[2025-01-09 16:24:36,566] INFO _appmap.env: appmap enabled: False
