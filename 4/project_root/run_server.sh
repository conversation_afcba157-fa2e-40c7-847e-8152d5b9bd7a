#!/bin/bash
# Kill any existing processes on port 8002
lsof -ti:8002 | xargs kill -9 2>/dev/null

# Run the server and store its PID
hypercorn wsgi:app --reload --bind 127.0.0.1:8002 & 
SERVER_PID=$!

# Wait for 1 hour then kill the server
sleep 3600 && kill -9 $SERVER_PID &

# Wait for either the server to exit or be killed
wait $SERVER_PID

# Clean up after timeout or manual interrupt
lsof -ti:8002 | xargs kill -9 2>/dev/null
