:root {
    /* Color Variables */
    --color-primary: #2196f3;
    --color-primary-dark: #1976d2;
    --color-gray-100: #F7FAFC;
    --color-gray-200: #EDF2F7;
    --color-gray-300: #E2E8F0;
    --color-gray-400: #CBD5E0;
    --color-gray-500: #A0AEC0;
    --color-gray-600: #718096;
    --color-gray-700: #4A5568;
    --color-gray-800: #2D3748;
    
    /* Agent Colors */
    --color-command: #2D3748;
    --color-viewfile: #2F855A;
    --color-writefile: #2B6CB0;
    --color-createfile: #805AD5;
    --color-chat: #6B46C1;
    
    /* Agent Background Colors */
    --bg-command: #F7FAFC;
    --bg-viewfile: #F0FFF4;
    --bg-writefile: #EBF8FF;
    --bg-createfile: #F3E8FF;
    --bg-chat: #FAF5FF;
    
    /* Status Colors */
    --color-success: #2F855A;
    --color-error: #C53030;
    --bg-success: #F0FFF4;
    --bg-error: #FFF5F5;
    
    /* Fonts */
    --font-mono: 'Fira Code', 'Menlo', 'Monaco', 'Courier New', monospace;
}

/* Base Message Styles */
.message {
    padding: 12px;
    margin: 8px 0;
    border-radius: 8px;
    max-width: 85%;
    white-space: pre-wrap;
    word-wrap: break-word;
    transition: background-color 0.2s;
}

.user-message {
    background-color: var(--color-gray-200);
    margin-left: 20%;
    color: var(--color-gray-800);
}

.ai-message {
    background-color: var(--color-gray-100);
    margin-right: 20%;
    color: var(--color-gray-800);
}

.output-message {
    background-color: var(--color-gray-100);
    margin: 12px 0;
    padding: 16px;
    border-radius: 8px;
    color: var(--color-gray-800);
    font-family: var(--font-mono);
}

/* Agent Badge Styles */
.agent-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    margin-right: 8px;
    font-size: 12px;
    font-weight: 500;
    color: white;
}

.agent-badge.command { background-color: var(--color-command); }
.agent-badge.viewfile { background-color: var(--color-viewfile); }
.agent-badge.writefile { background-color: var(--color-writefile); }
.agent-badge.createfile { background-color: var(--color-createfile); }
.agent-badge.chat { background-color: var(--color-chat); }

.output-message.command { background-color: var(--bg-command); }
.output-message.viewfile { background-color: var(--bg-viewfile); }
.output-message.writefile { background-color: var(--bg-writefile); }
.output-message.createfile { background-color: var(--bg-createfile); }
.output-message.chat { background-color: var(--bg-chat); }

/* Code Block Styles */
.code-block {
    margin: 16px 0;
    border-radius: 8px;
    overflow: hidden;
    background-color: #1e1e1e;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.code-block pre {
    margin: 0;
    padding: 16px;
    font-family: var(--font-mono);
    font-size: 14px;
    line-height: 1.5;
    overflow-x: auto;
}

.code-block-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background-color: #2d2d2d;
    color: white;
}

/* Split Panel Layout */
.split-container {
    display: flex;
    gap: 1rem;
    height: calc(100vh - 120px);
    margin: -1.5rem;
    padding: 1.5rem;
}

.panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.panel-header {
    padding: 1rem;
    background: var(--color-gray-100);
    border-bottom: 1px solid var(--color-gray-300);
    font-weight: 600;
    color: var(--color-gray-800);
}

.panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.panel-divider {
    width: 4px;
    margin: 0 -0.5rem;
    background: var(--color-gray-300);
    cursor: col-resize;
}

/* Input Area */
.input-area {
    position: sticky;
    bottom: 0;
    background: white;
    padding: 1rem;
    border-top: 1px solid var(--color-gray-300);
}

.input-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.input-container textarea {
    flex: 1;
    padding: 8px;
    border: 1px solid var(--color-gray-300);
    border-radius: 4px;
    font-size: 14px;
    resize: vertical;
    min-height: 40px;
}

.input-container button {
    padding: 10px 20px;
    background-color: var(--color-primary);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.input-container button:hover {
    background-color: var(--color-primary-dark);
}

/* Scrollbar Styles */
.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: var(--color-gray-100);
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: var(--color-gray-400);
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--color-gray-500);
}

/* Task Status Styles */
.task-status {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    margin-left: 8px;
}

.task-status.finished {
    background-color: var(--bg-success);
    color: var(--color-success);
    border: 1px solid #9AE6B4;
}

.task-status.incomplete {
    background-color: var(--bg-error);
    color: var(--color-error);
    border: 1px solid #FEB2B2;
}

/* Loading Animation */
.loading {
    display: inline-block;
    margin-left: 8px;
}

.loading::after {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid var(--color-gray-200);
    border-top: 2px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
    background-color: var(--bg-error);
    color: var(--color-error);
    margin: 12px 0;
    padding: 12px;
    border-radius: 8px;
}

.execute-button {
    display: block;
    width: 100%;
    padding: 8px 16px;
    background-color: var(--color-primary);
    color: white;
    border: none;
    border-top: 1px solid #1e1e1e;
    cursor: pointer;
    transition: background-color 0.2s;
}

.execute-button:hover {
    background-color: var(--color-primary-dark);
}

.execute-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.output {
    padding: 12px 16px;
    background-color: var(--color-gray-100);
    border-top: 1px solid var(--color-gray-300);
    font-family: var(--font-mono);
    white-space: pre-wrap;
    color: var(--color-gray-800);
}

.error {
    color: var(--color-error);
}

/* Confirmation Dialog */
.confirmation-buttons {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.confirm-button {
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    border: none;
    font-weight: 500;
    font-size: 14px;
    transition: background-color 0.2s, opacity 0.2s;
}

.confirm-button.accept {
    background-color: var(--color-primary);
    color: white;
}

.confirm-button.reject {
    background-color: var(--color-gray-200);
    color: var(--color-gray-800);
}

.confirm-button:hover {
    background-color: var(--color-primary-dark);
}

.confirm-button.reject:hover {
    background-color: var(--color-gray-300);
}

.confirm-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Scrollbar styling */
#chatMessages::-webkit-scrollbar {
    width: 8px;
}

#chatMessages::-webkit-scrollbar-track {
    background: var(--color-gray-100);
}

#chatMessages::-webkit-scrollbar-thumb {
    background: var(--color-gray-400);
    border-radius: 4px;
}

#chatMessages::-webkit-scrollbar-thumb:hover {
    background: var(--color-gray-500);
}
