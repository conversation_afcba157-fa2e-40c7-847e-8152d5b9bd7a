// Global language map
const langMap = {
    python: { language: 'python', display: 'Python' },
    bash: { language: 'bash', display: 'Shell' }
};

// Extended language map for file extensions
const fileExtensionToLanguageMap = {
    py: 'python',
    js: 'javascript',
    html: 'html',
    css: 'css',
    json: 'json',
    sh: 'bash',
    md: 'markdown',
    sql: 'sql',
    yml: 'yaml',
    yaml: 'yaml',
    xml: 'xml',
    java: 'java',
    cpp: 'cpp',
    c: 'c',
    cs: 'csharp',
    go: 'go',
    rs: 'rust',
    rb: 'ruby',
    php: 'php'
};

// Agent type configurations
const AGENT_TYPES = {
    AI: {
        type: 'ai',
        useOutputPanel: false,
        hasCodeOutput: false,
        hasExecutionOutput: false,
        displayName: 'AI'
    },
    COMMAND: {
        type: 'command',
        useOutputPanel: true,
        hasCodeOutput: true,
        hasExecutionOutput: false,  // command只显示命令，不显示execution output
        displayName: 'COMMAND',
        defaultLanguage: 'bash'
    },
    WRITE_FILE: {
        type: 'writefile',
        useOutputPanel: true,
        hasCodeOutput: true,
        hasExecutionOutput: true,
        displayName: 'WRITE FILE'
    },
    CREATE_FILE: {
        type: 'createfile',
        useOutputPanel: true,
        hasCodeOutput: false,
        hasExecutionOutput: true,
        displayName: 'CREATE FILE'
    },
    VIEW_FILE: {
        type: 'viewfile',
        useOutputPanel: true,
        hasCodeOutput: true,
        hasExecutionOutput: false,
        displayName: 'VIEW FILE'
    }
};

class CodeAssistant {
    constructor() {
        this.chatMessages = document.getElementById('chatMessages');
        this.outputMessages = document.getElementById('outputMessages');
        this.userInput = document.getElementById('userInput');
        this.sendButton = document.getElementById('sendButton');
        this.panelDivider = document.getElementById('panelDivider');
        this.taskStatus = document.getElementById('taskStatus');
        
        this.setupEventListeners();
        // this.setupPanelResizing();
    }
    
    // 发送按钮点击
    setupEventListeners() {
        this.sendButton.addEventListener('click', () => this.sendMessage());
        this.userInput.addEventListener('keydown', (e) => {
            // 如果按下的是回车键且不是 shift 键，就发送消息
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
    }

    // 更新任务状态
    updateTaskStatus(status) {
        this.taskStatus.textContent = status;
        this.taskStatus.className = 'task-status ' + status.toLowerCase();
    }
    
    // 发送消息
    async sendMessage() {
        const message = this.userInput.value.trim();
        if (!message) return;

        this.userInput.value = '';
        this.appendMessage(message, 'user');
        
        // Reset task status to Incomplete at the start
        this.updateTaskStatus('Incomplete');

        try {
            // 在聊天界面中创建并显示一个加载消息，消息内容为“Assistant: ”，并确保聊天界面滚动到底部
            const loadingDiv = this.createLoadingMessage();
            
            // Stream the response from _CodeGenerator as prompt
            // Three type of response: 1. streaming; 2. complete; 3. execute response
            const response = await fetch('/ai/generate/stream', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message })
            });

            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            // Code_generator.generate_code
            while (true) { //  循环读取响应数据
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const events = chunk.split('\n\n');

                events.forEach(event => this.processSSEEvent(event, loadingDiv)); //  遍历SSE events
            }
        } catch (error) {
            this.appendMessage(`Error: ${error.message}`, 'error');
        }
    }

    // 在聊天界面中创建并显示一个加载消息，消息内容为“Assistant: ”，并确保聊天界面滚动到底部
    createLoadingMessage() {
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'message ai-message loading';
        loadingDiv.textContent = 'Assistant: ';
        const streamingContent = document.createElement('span');
        loadingDiv.appendChild(streamingContent);
        this.chatMessages.appendChild(loadingDiv);
        this.scrollToBottom(this.chatMessages);
        return loadingDiv;
    }


    /** 
     * 处理 SSE 事件：
     * 解析成功且streaming，则调用updateStreamingContent方法更新流内容。
     * 解析成功且不为streaming，则移除加载状态元素并调用handleSSEData方法处理数据。
     * 如果解析失败，则捕获异常并输出错误信息到控制台。
     */
    processSSEEvent(event, loadingDiv) {
        if (!event.trim()) return;

        const dataMatch = event.match(/data: (.+)$/m);
        if (!dataMatch) return;

        try {
            const data = JSON.parse(dataMatch[1]);
            if (data.status === 'streaming') { //  如果数据状态为streaming，则更新流内容
                const streamingContent = this.chatMessages.querySelector('.loading span');
                streamingContent.textContent += data.chunk;
                this.scrollToBottom(this.chatMessages);
            } else {
                loadingDiv.remove();
                this.handleSSEData(data);
            }
        } catch (error) {
            console.error('Failed to process SSE event:', error);
        }
    }

    /**
     * 处理 complete 和 execute 两种情况的 SSE 数据
     * complete: handleCompleteData
     * execute: handleExecuteData
     */
    async handleSSEData(data) {
        // Update task status if present in the data
        if (data.task_status) {
            this.updateTaskStatus(data.task_status); //  更新任务状态
        }

        // Handle data based on status
        if (data.status === 'complete') { //  如果数据状态为complete，则处理完成数据
            // 将分析结果、代码块追加到聊天消息列表中。
            this.appendMessage(data.analysis, 'ai', data); //  添加分析消息（_CodeGenerator.txt 中的 <analyse>）
            this.appendMessage(`Codii: ${data.chat}`, 'ai', data); //  添加当前步骤消息
            if (data.code) { //  如果生成了 python 代码，添加代码块
                this.appendCodeBlock(data.code, data.type);
                // Execute the generated code
                await this.executeGeneratedCode(data.code);
            }
        }
    }

    async executeGeneratedCode(code) {
        try {
            const response = await fetch('/ai/generate/execute', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    code: code,
                    type: 'python'
                })
            });

            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.trim() === '') continue;
                    if (!line.startsWith('data: ')) continue;

                    const eventData = JSON.parse(line.slice(6));
                    if (eventData.status === 'execute' && eventData.result) {
                        const resultObj = typeof eventData.result === 'string'
                            ? JSON.parse(eventData.result)
                            : eventData.result;
                        this.displayExecutionResults(resultObj, eventData);
                    } else if (eventData.status === 'error') {
                        console.error('Execution error:', eventData.error);
                        this.appendMessage(`Error executing code: ${eventData.error}`, 'error');
                    }
                }
            }
        } catch (error) {
            console.error('Failed to execute code:', error);
            this.appendMessage(`Error executing code: ${error.message}`, 'error');
        }
    }

    createConfirmationButtons(outputDiv, resultObj, callback) {
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'confirmation-buttons';
        
        const acceptButton = document.createElement('button');
        acceptButton.className = 'confirm-button accept';
        acceptButton.textContent = 'Accept';
        
        const rejectButton = document.createElement('button');
        rejectButton.className = 'confirm-button reject';
        rejectButton.textContent = 'Reject';
        
        acceptButton.onclick = () => {
            buttonContainer.remove();
            callback(true, resultObj);
        };
        
        rejectButton.onclick = () => {
            // Only remove the confirmation buttons instead of the entire output
            buttonContainer.remove();
            callback(false, resultObj);
        };
        
        buttonContainer.appendChild(acceptButton);
        buttonContainer.appendChild(rejectButton);
        outputDiv.appendChild(buttonContainer);
    }
    
    // 将执行结果显示在页面上
    displayExecutionResults(resultObj, data) {
        const outputDiv = document.createElement('div');
        outputDiv.className = 'message output-message';
        
        const agentType = data?.agent_type || resultObj.type;
        const agentConfig = Object.values(AGENT_TYPES).find(config => config.type === agentType);
        
        if (agentConfig) {
            outputDiv.classList.add(agentConfig.type);
            
            const badge = document.createElement('span');
            badge.className = `agent-badge ${agentConfig.type}`;
            badge.textContent = agentConfig.displayName;
            outputDiv.appendChild(badge);
        }
        
        if (agentConfig?.hasCodeOutput) {
            this.appendCodeOutput(outputDiv, resultObj);
            
            if (agentConfig.type === AGENT_TYPES.COMMAND.type) {
                const executionContainer = document.createElement('div');
                executionContainer.className = 'execution-container';
                outputDiv.appendChild(executionContainer);
    
                const commandWord = resultObj.output.trim().split(' ')[0];
                // 不管是否安全命令，都使用相同的执行方式
                this.createConfirmationButtons(outputDiv, resultObj, (accepted, result) => {
                    if (accepted) {
                        this.executeCommand(result.output, executionContainer);
                    }
                });
            }
        } else {
            const outputContent = document.createElement('span');
            outputContent.textContent = resultObj.output;
            outputDiv.appendChild(outputContent);
        }
        
        this.outputMessages.appendChild(outputDiv);
        
        if (resultObj.execution_output && agentConfig?.hasExecutionOutput) {
            this.appendExecutionOutput(resultObj, data);
        }
    }

    isSafeCommand(command) {
        const SAFE_COMMANDS = new Set([
            'pwd',    // print working directory
            'ls',     // list directory contents
            'dir',    // list directory contents (Windows)
            'echo',   // print text
            'cat',    // view file contents
            'head',   // view start of file
            'tail',   // view end of file
            'wc',     // count lines/words/chars
            'grep',   // search text
            'find',   // search files
            'tree',   // show directory structure
            'which',  // show command location
            'type',   // show command type
        ]);
        
        return SAFE_COMMANDS.has(command);
    }
    
    
    async executeCommand(command, executionContainer) {
        try {
            const response = await fetch('/execute', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ 
                    code: command,
                    type: 'bash'  // 改为bash类型
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                const execContent = document.createElement('div');
                execContent.className = 'execution-output command';
                execContent.textContent = result.output || '命令执行成功';  // 如果没有输出也显示成功信息
                executionContainer.appendChild(execContent);
            } else {
                const errorContent = document.createElement('div');
                errorContent.className = 'execution-output error';
                errorContent.textContent = result.output;
                executionContainer.appendChild(errorContent);
            }
            
            this.scrollToBottom(this.outputMessages);
        } catch (error) {
            const errorContent = document.createElement('div');
            errorContent.className = 'execution-output error';
            errorContent.textContent = `Error executing command: ${error.message}`;
            executionContainer.appendChild(errorContent);
        }
    }
    
    
    


    // 将聊天输出内容添加到指定的聊天消息面板中，并确保消息面板滚动到最新消息
    appendChatOutput(outputDiv, resultObj) {
        const chatContent = document.createElement('span');
        chatContent.className = 'chat-content'; // Add class for styling if needed
        chatContent.textContent = resultObj.output;
        outputDiv.appendChild(chatContent);
        
        // Ensure the chat message is added to the chatMessages panel
        this.chatMessages.appendChild(outputDiv);
        this.scrollToBottom(this.chatMessages);
    }

    // 将代码输出添加到指定的HTML元素中，并使用Prism.js库对代码进行语法高亮
    appendCodeOutput(outputDiv, resultObj) {
        const codeBlock = document.createElement('div');
        codeBlock.className = 'code-block';
        
        const pre = document.createElement('pre');
        pre.className = 'line-numbers';
        
        const code = document.createElement('code');
        
        // 确定语言类型
        let language = 'bash';  // default for command
        if (resultObj.type === 'writefile' || resultObj.type === 'createfile') {
            // Extract file extension from execution_output
            const fileMatch = resultObj.execution_output.match(/(?:Created file|Created empty file at): .*\.(.*?)$/);
            if (fileMatch) {
                const ext = fileMatch[1].toLowerCase();
                language = fileExtensionToLanguageMap[ext] || 'plaintext';
            }
        }
        
        code.className = `language-${language}`;
        
        // Format command output to remove prompt
        code.textContent = resultObj.type === 'command' 
            ? resultObj.output.replace(/^\$ /, '')
            : resultObj.output;
        
        pre.appendChild(code);
        codeBlock.appendChild(pre);
        outputDiv.appendChild(codeBlock);
        
        // Trigger Prism to highlight the new code
        Prism.highlightElement(code);
    }


    // 将执行结果输出到页面上，并在页面上显示执行结果的内容。如果执行结果类型是 writefile，则内容会显示 filepath。
    appendExecutionOutput(resultObj, data) {
        const execDiv = document.createElement('div');
        execDiv.className = 'message output-message execution-output';
        execDiv.style.marginTop = '4px';
        
        const agentType = data?.agent_type || resultObj.type;
        const agentConfig = Object.values(AGENT_TYPES).find(config => config.type === agentType);
        
        if (agentConfig) {
            execDiv.classList.add(agentConfig.type);
        }
        
        // Add execution output content with formatting for different file operations
        const execContent = document.createElement('span');
        if (resultObj.type === 'writefile') {
            execContent.textContent = `📝 Written Content:\n${resultObj.execution_output}\n`;
        } else if (resultObj.type === 'createfile') {
            execContent.textContent = `📁 ${resultObj.execution_output}\n`;
        } else {
            execContent.textContent = resultObj.execution_output;
        }
        execDiv.appendChild(execContent);
        
        this.outputMessages.appendChild(execDiv);
    }

    /**
     * 根据消息类型和内容创建并添加消息到对应面板
     * @param {string|object} content - 消息内容
     * @param {string} type - 消息类型 ('ai' | 'error' | 'user')
     * @param {object} data - 附加数据
     */
    appendMessage(content, type, data = null) {
        const messageDiv = this.createMessageElement(type, data);
        
        // 处理错误消息
        if (type === 'error') {
            messageDiv.textContent = content;
            this.chatMessages.appendChild(messageDiv);
            this.scrollToBottom(this.chatMessages);
            return;
        }

        const agentType = data?.agent_type;
        const agentConfig = agentType && Object.values(AGENT_TYPES).find(config => config.type === agentType);
        
        // 确定目标面板
        const targetPanel = agentConfig?.useOutputPanel
                            ? this.outputMessages 
                            : this.chatMessages;
        
        // 根据内容类型（json）处理内容
        if (typeof content === 'string' && data?.type === 'json') {
            this.handleJsonContent(messageDiv, content, data, targetPanel);
        } else {
            this.appendSimpleContent(messageDiv, content, targetPanel);
        }
    }

    /**
     * 创建消息元素并添加基本样式
     */
    createMessageElement(type, data) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;
        
        const agentType = data?.agent_type;
        const agentConfig = agentType && Object.values(AGENT_TYPES).find(config => config.type === agentType);
        
        if (agentConfig?.useOutputPanel) {
            const badge = document.createElement('span');
            messageDiv.appendChild(badge);
            badge.className = `agent-badge ${agentConfig.type}`;
            badge.textContent = agentConfig.displayName;
        }
        return messageDiv;
    }

   
    /**
     * 处理 JSON 内容
     */
    handleJsonContent(messageDiv, content, data, targetPanel) {
        try {
            const resultObj = JSON.parse(content);
            const agentConfig = Object.values(AGENT_TYPES).find(config => config.type === resultObj.type);
            
            if (agentConfig?.hasCodeOutput) {
                this.handleCodeOutput(messageDiv, resultObj, data);
            } else {
                this.appendSimpleContent(messageDiv, content, targetPanel);
            }
        } catch (e) {
            this.appendSimpleContent(messageDiv, content, targetPanel);
        }
    }

    // 添加简单内容
    appendSimpleContent(messageDiv, content, targetPanel) {
        messageDiv.textContent = typeof content === 'string' ? content : JSON.stringify(content, null, 2);
        targetPanel.appendChild(messageDiv);
        this.scrollToBottom(targetPanel);
    }

    /**
     * 处理代码输出
     */
    handleCodeOutput(messageDiv, resultObj, data) {
        // 添加代码块
        const codeBlock = this.createCodeBlock(resultObj);
        messageDiv.appendChild(codeBlock);
        this.outputMessages.appendChild(messageDiv);

        // 添加执行输出（如果有）
        if (resultObj.execution_output) {
            const execDiv = this.createExecutionOutput(resultObj, data);
            this.outputMessages.appendChild(execDiv);
        }

        this.scrollToBottom(this.outputMessages);
    }

    /**
     * 创建代码块并确定语言类型
     * 如果是writefile类型，从文件扩展名确定语言
     * 如果是command类型，默认使用bash
     */
    createCodeBlock(resultObj) {
        const codeBlock = document.createElement('div');
        codeBlock.className = 'code-block';

        // Create header
        const header = document.createElement('div');
        header.className = 'code-block-header';
        header.innerHTML = `
            <span>Code ${langMap[resultObj.type].display}
                <span class="type-badge type-${resultObj.type}">${resultObj.type}</span>
            </span>
        `;
        codeBlock.appendChild(header);

        // Create code content
        const pre = document.createElement('pre');
        pre.className = 'line-numbers';
        
        const codeElement = document.createElement('code');
        // Use appropriate language class for Prism
        codeElement.className = `language-${langMap[resultObj.type].language}`;
        codeElement.textContent = resultObj.output;
        
        pre.appendChild(codeElement);
        codeBlock.appendChild(pre);

        // Create execute button
        const executeButton = document.createElement('button');
        executeButton.className = 'execute-button';
        executeButton.textContent = `Execute ${resultObj.type} code`;
        executeButton.onclick = () => this.executeCode(resultObj.output, resultObj.type, executeButton);
        codeBlock.appendChild(executeButton);

        this.chatMessages.appendChild(codeBlock);
        this.scrollToBottom(this.chatMessages);

        // Trigger Prism to highlight the new code
        Prism.highlightElement(codeElement);
    }

    /**
     * 创建执行输出元素
     */
    createExecutionOutput(resultObj, data) {
        const execDiv = document.createElement('div');
        execDiv.className = 'message output-message execution-output';
        
        const agentType = data?.agent_type || resultObj.type;
        const agentConfig = Object.values(AGENT_TYPES).find(config => config.type === agentType);
        
        if (agentConfig) {
            execDiv.classList.add(agentConfig.type);
        }
        
        const execContent = document.createElement('span');
        execContent.textContent = resultObj.execution_output;
        execDiv.appendChild(execContent);
        
        return execDiv;
    }

    scrollToBottom(panel) {
        panel.scrollTop = panel.scrollHeight;
    }

    /**
     * 执行代码并显示结果
     * 1. 禁用执行按钮并显示加载状态
     * 2. 发送代码到服务器执行
     * 3. 显示执行结果
     * 4. 恢复按钮状态
     */
    async executeCode(code, type, button) {
        // 1. 更新按钮状态为加载中
        button.disabled = true;
        button.innerHTML = 'Executing... <span class="loading"></span>';

        try {
            // 2. 发送代码到服务器执行
            const response = await fetch('/execute', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ code, type })
            });
            const result = await response.json();

            // 3. 显示执行结果
            const outputDiv = document.createElement('div');
            outputDiv.className = `output${result.output.includes('Error:') ? ' error' : ''}`;
            outputDiv.textContent = result.output;

            // 替换现有输出（如果有）
            const existingOutput = button.parentNode.querySelector('.output');
            if (existingOutput) existingOutput.remove();
            button.parentNode.appendChild(outputDiv);
        } catch (error) {
            this.appendMessage(`Error: ${error.message}`, 'error');
        } finally {
            // 4. 恢复按钮状态
            button.disabled = false;
            button.textContent = `Execute ${type} code`;
        }
    }

    // 添加 generate_code 生成的 python 代码 
    appendCodeBlock(code, type) {
        const codeBlock = document.createElement('div');
        codeBlock.className = 'code-block';

        // Create header
        const header = document.createElement('div');
        header.className = 'code-block-header';
        header.innerHTML = `
            <span>Code ${langMap[type].display}
                <span class="type-badge type-${type}">${type}</span>
            </span>
        `;
        codeBlock.appendChild(header);

        // Create code content
        const pre = document.createElement('pre');
        pre.className = 'line-numbers';
        
        const codeElement = document.createElement('code');
        // Use appropriate language class for Prism
        codeElement.className = `language-${langMap[type].language}`;
        codeElement.textContent = code;
        
        pre.appendChild(codeElement);
        codeBlock.appendChild(pre);

        // Create execute button
        const executeButton = document.createElement('button');
        executeButton.className = 'execute-button';
        executeButton.textContent = `Execute ${type} code`;
        executeButton.onclick = () => this.executeCode(code, type, executeButton);
        codeBlock.appendChild(executeButton);

        this.chatMessages.appendChild(codeBlock);
        this.scrollToBottom(this.chatMessages);

        // Trigger Prism to highlight the new code
        Prism.highlightElement(codeElement);
    }

    
}

// Initialize the app
document.addEventListener('DOMContentLoaded', () => {
    window.codeAssistant = new CodeAssistant();
});