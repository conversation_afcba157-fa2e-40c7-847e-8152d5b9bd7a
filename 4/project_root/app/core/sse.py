from flask import Response
from typing import AsyncGenerator, Any
import json
import asyncio

class SSE:
    @staticmethod
    async def format_sse(data: Any, event: str = None) -> str:
        """Format data according to SSE spec"""
        if isinstance(data, (dict, list)):
            msg = f"data: {json.dumps(data)}\n"
        else:
            msg = f"data: {data}\n"
            
        if event is not None:
            msg = f"event: {event}\n{msg}"
        return f"{msg}\n"

    @staticmethod
    async def stream(generator_function: AsyncGenerator) -> Response:
        """Create a response streaming the data from the async generator"""
        async def async_generator():
            async for data in generator_function:
                yield data

        return Response(
            async_generator(),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'X-Accel-Buffering': 'no',  # Disable buffering in Nginx
                'Content-Type': 'text/event-stream',
                'Access-Control-Allow-Origin': '*'  # Enable CORS
            }
        )