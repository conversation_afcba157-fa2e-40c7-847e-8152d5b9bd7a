from quart import Quart
from quart_cors import cors
from app.routes.main import main as main_blueprint
from app.agents.utils import create_agent, AGENT_REGISTRY
from app.core.agent_context import agent_context

def create_app():
    app = Quart(__name__)
    app = cors(app)
    
    # Initialize agents after app creation but before registering blueprints
    agent_context.set_create_agent(create_agent)
    agent_context.update_registry(AGENT_REGISTRY)
    
    # Register blueprints
    app.register_blueprint(main_blueprint)
    
    return app

app = create_app()