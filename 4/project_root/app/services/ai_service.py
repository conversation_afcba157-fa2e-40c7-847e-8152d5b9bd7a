import json
import requests
from typing import Generator, Optional, List
from config import Config
from .prompt_manager import PromptManager
from collections import deque

class AIService:
    def __init__(self):
        self.prompt_manager = PromptManager()
        self.message_history = deque(maxlen=5)  # Store last 5 rounds of conversation

    def add_to_history(self, user_msg: str, assistant_msg: str):
        """Add a conversation exchange to history"""
        self.message_history.append({
            "user": user_msg,
            "assistant": assistant_msg
        })

    def get_messages_with_history(self, new_message: str) -> List[dict]:
        """Combine history with new message"""
        messages = []
        for exchange in self.message_history:
            messages.extend([
                {"role": "user", "content": exchange["user"]},
                {"role": "assistant", "content": exchange["assistant"]}
            ])
        messages.append({"role": "user", "content": new_message})
        return messages

    def ask_claude(self, message: str) -> str:
        """Send a message to <PERSON> and get the response (non-streaming)"""
        headers = {
            "x-api-key": Config.ANTHROPIC_API_KEY,
            "anthropic-version": "2023-06-01",
            "content-type": "application/json"
        }
        data = {
            "model": Config.CLAUDE_MODEL,
            "max_tokens": 1024,
            "messages": self.get_messages_with_history(message)
        }
        response = requests.post(Config.ANTHROPIC_API_URL, headers=headers, json=data)
        try:
            reply = response.json()['content'][0]['text']
            self.add_to_history(message, reply)
            return reply
        except Exception as e:
            print(f"Error in Claude response: {response.json()}")
            return None

    def ask_claude_stream(self, message: str) -> Generator[str, None, None]:
        """Send a message to Claude and stream the response"""
        headers = {
            "x-api-key": Config.ANTHROPIC_API_KEY,
            "anthropic-version": "2023-06-01",
            "content-type": "application/json",
            "accept": "text/event-stream"
        }
        data = {
            "model": Config.CLAUDE_MODEL,
            "max_tokens": 1024,
            "messages": self.get_messages_with_history(message),
            "stream": True
        }

        try:
            response = requests.post(Config.ANTHROPIC_API_URL, headers=headers, json=data, stream=True)
            full_response = ""
            
            for line in response.iter_lines():
                if not line:
                    continue
                    
                try:
                    line_text = line.decode('utf-8')
                    if not line_text.startswith('data: '):
                        continue
                        
                    json_str = line_text[6:]  # Remove 'data: ' prefix
                    if json_str.strip() == '[DONE]':
                        break
                        
                    event = json.loads(json_str)
                    if event.get('type') == 'message_stop':
                        break
                        
                    if 'delta' in event and 'text' in event['delta']:
                        delta = event['delta']['text']
                        full_response += delta
                        yield delta
                        
                except Exception as e:
                    print(f"Error parsing stream line: {str(e)}")
                    continue
            
            # After streaming is complete, update message history
            if full_response:
                self.add_to_history(message, full_response)
            
        except Exception as e:
            print(f"Error in Claude streaming response: {e}")
            yield f"Error: {str(e)}"


# Create a singleton instance
ai_service = AIService()