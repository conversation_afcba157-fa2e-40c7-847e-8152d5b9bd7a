{"cells": [{"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "def ask_claude_stream(message: str):\n", "    \"\"\"Send a message to <PERSON> and stream the response\"\"\"\n", "    headers = {\n", "        \"x-api-key\": '************************************************************************************************************',\n", "        \"anthropic-version\": \"2023-06-01\",\n", "        \"content-type\": \"application/json\",\n", "        \"accept\": \"text/event-stream\"\n", "    }\n", "    data = {\n", "        \"model\": 'claude-3-5-sonnet-20240620',\n", "        \"max_tokens\": 1024,\n", "        \"messages\": [\n", "            {\"role\": \"user\", \"content\": message}\n", "        ],\n", "        \"stream\": True\n", "    }\n", "\n", "    response = requests.post('https://api.anthropic.com/v1/messages', headers=headers, json=data, stream=True)\n", "    for line in response.iter_lines():\n", "        print(line)\n", "        "]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'event: message_start'\n", "b'data: {\"type\":\"message_start\",\"message\":{\"id\":\"msg_01ArCMsVWRhMWKrxP73yA9Pk\",\"type\":\"message\",\"role\":\"assistant\",\"model\":\"claude-3-5-sonnet-20240620\",\"content\":[],\"stop_reason\":null,\"stop_sequence\":null,\"usage\":{\"input_tokens\":8,\"cache_creation_input_tokens\":0,\"cache_read_input_tokens\":0,\"output_tokens\":5}}    }'\n", "b''\n", "b'event: content_block_start'\n", "b'data: {\"type\":\"content_block_start\",\"index\":0,\"content_block\":{\"type\":\"text\",\"text\":\"\"}}'\n", "b''\n", "b'event: ping'\n", "b'data: {\"type\": \"ping\"}'\n", "b''\n", "b'event: content_block_delta'\n", "b'data: {\"type\":\"content_block_delta\",\"index\":0,\"delta\":{\"type\":\"text_delta\",\"text\":\"Hello! How can I\"} }'\n", "b''\n", "b'event: content_block_delta'\n", "b'data: {\"type\":\"content_block_delta\",\"index\":0,\"delta\":{\"type\":\"text_delta\",\"text\":\" assist you today? Feel free to ask me any questions or let\"}               }'\n", "b''\n", "b'event: content_block_delta'\n", "b'data: {\"type\":\"content_block_delta\",\"index\":0,\"delta\":{\"type\":\"text_delta\",\"text\":\" me know if there\\'s anything you\\'d like help with.\"}  }'\n", "b''\n", "b'event: content_block_stop'\n", "b'data: {\"type\":\"content_block_stop\",\"index\":0            }'\n", "b''\n", "b'event: message_delta'\n", "b'data: {\"type\":\"message_delta\",\"delta\":{\"stop_reason\":\"end_turn\",\"stop_sequence\":null},\"usage\":{\"output_tokens\":33}        }'\n", "b''\n", "b'event: message_stop'\n", "b'data: {\"type\":\"message_stop\"       }'\n", "b''\n"]}], "source": ["ask_claude_stream('hi')"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["View the contents of a file. The lines of the file are 0-indexed, and the output of this tool call will be the file contents from StartLine to EndLine, together with a summary of the lines outside of StartLine and EndLine. Note that this call can view at most 200 lines at a time.\n", "\n", "When using this tool to gather information, it's your responsibility to ensure you have the COMPLETE context. Specifically, each time you call this command you should:\n", "1) Assess if the file contents you viewed are sufficient to proceed with your task.\n", "2) Take note of where there are lines not shown. These are represented by <... XX more lines from [code item] not shown ...> in the tool response.\n", "3) If the file contents you have viewed are insufficient, and you suspect they may be in lines not shown, proactively call the tool again to view those lines.\n", "4) When in doubt, call this tool again to gather more information. Remember that partial file views may miss critical dependencies, imports, or functionality.\n", "\n"]}], "source": ["print('View the contents of a file. The lines of the file are 0-indexed, and the output of this tool call will be the file contents from StartLine to EndLine, together with a summary of the lines outside of StartLine and EndLine. Note that this call can view at most 200 lines at a time.\\n\\nWhen using this tool to gather information, it\\'s your responsibility to ensure you have the COMPLETE context. Specifically, each time you call this command you should:\\n1) Assess if the file contents you viewed are sufficient to proceed with your task.\\n2) Take note of where there are lines not shown. These are represented by <... XX more lines from [code item] not shown ...> in the tool response.\\n3) If the file contents you have viewed are insufficient, and you suspect they may be in lines not shown, proactively call the tool again to view those lines.\\n4) When in doubt, call this tool again to gather more information. Remember that partial file views may miss critical dependencies, imports, or functionality.\\n')"]}], "metadata": {"kernelspec": {"display_name": "codegeex", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 2}